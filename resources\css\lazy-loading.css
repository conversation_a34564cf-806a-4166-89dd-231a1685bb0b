/* Lazy Loading CSS Effects */

.lazy-loading {
    opacity: 0.5;
    filter: blur(2px);
    transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
}

.lazy-loaded {
    opacity: 1;
    filter: none;
}

.lazy-error {
    opacity: 0.7;
    filter: grayscale(100%);
}

.lazy-content-loading {
    opacity: 0.5;
    position: relative;
    min-height: 100px;
}

.lazy-content-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: lazy-spin 1s linear infinite;
}

.lazy-content-loaded {
    opacity: 1;
}

.lazy-content-error {
    opacity: 0.7;
    background-color: #fee;
    border: 1px solid #fcc;
    border-radius: 4px;
    padding: 1rem;
}

@keyframes lazy-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton loading animation */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Skeleton variants */
.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
    width: 80%;
}

.skeleton-title {
    height: 1.5rem;
    margin-bottom: 1rem;
}

.skeleton-image {
    height: 200px;
    margin-bottom: 1rem;
}

.skeleton-card {
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Fade-in animations */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Staggered animations */
.stagger-children > * {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.stagger-children.visible > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.visible > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.visible > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.visible > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.visible > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.visible > *:nth-child(6) { transition-delay: 0.6s; }

.stagger-children.visible > * {
    opacity: 1;
    transform: translateY(0);
}

/* Image placeholder */
.image-placeholder {
    background-color: #f3f4f6;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

/* Progressive image loading */
.progressive-image {
    position: relative;
    overflow: hidden;
}

.progressive-image img {
    transition: opacity 0.3s ease-in-out;
}

.progressive-image .placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(5px);
    transform: scale(1.1);
    z-index: 1;
}

.progressive-image .main-image {
    position: relative;
    z-index: 2;
    opacity: 0;
}

.progressive-image .main-image.loaded {
    opacity: 1;
}

/* Responsive utilities */
@media (prefers-reduced-motion: reduce) {
    .lazy-loading,
    .lazy-loaded,
    .fade-in,
    .fade-in-up,
    .fade-in-left,
    .fade-in-right,
    .stagger-children > * {
        transition: none;
        animation: none;
    }
    
    .skeleton {
        animation: none;
        background: #f0f0f0;
    }
}
