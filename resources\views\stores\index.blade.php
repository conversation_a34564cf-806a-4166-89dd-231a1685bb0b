@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                🏪 Popular Stores & Brands
            </h1>
            <p class="text-xl text-purple-100 max-w-2xl mx-auto">
                Discover amazing deals from your favorite stores. Shop with confidence and save money!
            </p>
        </div>
    </div>
</section>

<!-- Search & Filters -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
                <form action="{{ route('stores.index') }}" method="GET" class="relative">
                    <input type="text" 
                           name="search" 
                           placeholder="Search stores..." 
                           value="{{ request('search') }}"
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    @foreach(['category', 'country', 'sort'] as $param)
                        @if(request($param))
                            <input type="hidden" name="{{ $param }}" value="{{ request($param) }}">
                        @endif
                    @endforeach
                </form>
            </div>

            <!-- Category Filter -->
            <div>
                <select name="category" 
                        onchange="updateFilters()"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="">All Categories</option>
                    @foreach($categories ?? [] as $slug => $name)
                        <option value="{{ $slug }}" {{ request('category') === $slug ? 'selected' : '' }}>
                            {{ $name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Sort -->
            <div>
                <select name="sort" 
                        onchange="updateFilters()"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                    <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name A-Z</option>
                    <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                </select>
            </div>
        </div>

        <!-- Country Filter -->
        @if(isset($countries) && $countries->count() > 1)
        <div class="mt-4">
            <div class="flex flex-wrap gap-2">
                <span class="text-sm text-gray-600 py-2">Filter by country:</span>
                <button onclick="setCountryFilter('')" 
                        class="px-3 py-1 text-sm rounded-full {{ !request('country') ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                    All
                </button>
                @foreach($countries as $country)
                    <button onclick="setCountryFilter('{{ $country }}')" 
                            class="px-3 py-1 text-sm rounded-full {{ request('country') === $country ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        {{ $country }}
                    </button>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Featured Stores -->
@if(isset($featuredStores) && $featuredStores->count() > 0)
<section class="py-12 bg-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">⭐ Featured Stores</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            @foreach($featuredStores as $store)
                @include('components.store-card', ['store' => $store])
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Main Stores Grid -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(request()->hasAny(['search', 'category', 'country']))
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">
                    @if(request('search'))
                        Search Results for "{{ request('search') }}"
                    @else
                        Filtered Results
                    @endif
                </h2>
                <p class="text-gray-600 mt-1">{{ $stores->total() ?? 0 }} {{ Str::plural('store', $stores->total() ?? 0) }} found</p>
            </div>
        @else
            <h2 class="text-2xl font-bold text-gray-900 mb-8">🛍️ All Stores</h2>
        @endif

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
            @forelse($stores ?? [] as $store)
                @include('components.store-card', ['store' => $store])
            @empty
                <!-- Demo Content -->
                @for($i = 0; $i < 12; $i++)
                    @php
                        $demoStores = [
                            ['name' => 'Amazon', 'deals' => 25, 'color' => 'red', 'letter' => 'A'],
                            ['name' => 'Nike', 'deals' => 18, 'color' => 'blue', 'letter' => 'N'],
                            ['name' => 'McDonald\'s', 'deals' => 12, 'color' => 'yellow', 'letter' => 'M'],
                            ['name' => 'Apple', 'deals' => 8, 'color' => 'purple', 'letter' => 'A'],
                            ['name' => 'Starbucks', 'deals' => 15, 'color' => 'green', 'letter' => 'S'],
                            ['name' => 'Uber', 'deals' => 10, 'color' => 'pink', 'letter' => 'U'],
                            ['name' => 'Best Buy', 'deals' => 22, 'color' => 'indigo', 'letter' => 'B'],
                            ['name' => 'Target', 'deals' => 19, 'color' => 'red', 'letter' => 'T'],
                            ['name' => 'Walmart', 'deals' => 30, 'color' => 'blue', 'letter' => 'W'],
                            ['name' => 'Home Depot', 'deals' => 14, 'color' => 'orange', 'letter' => 'H'],
                            ['name' => 'Costco', 'deals' => 16, 'color' => 'blue', 'letter' => 'C'],
                            ['name' => 'eBay', 'deals' => 28, 'color' => 'yellow', 'letter' => 'E'],
                        ];
                        $store = $demoStores[$i % count($demoStores)];
                    @endphp
                    
                    <div class="card-hover text-center">
                        <div class="p-6">
                            <div class="w-16 h-16 bg-{{ $store['color'] }}-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                                <span class="text-2xl font-bold text-{{ $store['color'] }}-600">{{ $store['letter'] }}</span>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">{{ $store['name'] }}</h3>
                            <p class="text-sm text-gray-500 mb-3">{{ $store['deals'] }} deals</p>
                            <div class="space-y-2">
                                <button class="w-full btn-primary text-sm">View Deals</button>
                                <button class="w-full btn-outline text-xs">Visit Store</button>
                            </div>
                        </div>
                    </div>
                @endfor
            @endforelse
        </div>

        <!-- Pagination -->
        @if(isset($stores) && $stores->hasPages())
            <div class="mt-12">
                {{ $stores->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</section>

<!-- Store Categories -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">🏷️ Shop by Category</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @php
                $categories = [
                    ['name' => 'Fashion', 'icon' => '👗', 'count' => 45],
                    ['name' => 'Electronics', 'icon' => '📱', 'count' => 32],
                    ['name' => 'Food & Dining', 'icon' => '🍕', 'count' => 28],
                    ['name' => 'Travel', 'icon' => '✈️', 'count' => 18],
                    ['name' => 'Home & Garden', 'icon' => '🏠', 'count' => 25],
                    ['name' => 'Health & Beauty', 'icon' => '💄', 'count' => 22],
                    ['name' => 'Sports', 'icon' => '⚽', 'count' => 15],
                    ['name' => 'Books', 'icon' => '📚', 'count' => 12],
                    ['name' => 'Automotive', 'icon' => '🚗', 'count' => 8],
                    ['name' => 'Pet Supplies', 'icon' => '🐕', 'count' => 10],
                    ['name' => 'Baby & Kids', 'icon' => '👶', 'count' => 14],
                    ['name' => 'Office', 'icon' => '💼', 'count' => 9],
                ];
            @endphp
            
            @foreach($categories as $category)
                <a href="{{ route('stores.index', ['category' => strtolower($category['name'])]) }}" 
                   class="card-hover text-center p-4 group">
                    <div class="text-3xl mb-2">{{ $category['icon'] }}</div>
                    <h3 class="font-medium text-gray-900 group-hover:text-purple-600 transition-colors duration-200">{{ $category['name'] }}</h3>
                    <p class="text-sm text-gray-500">{{ $category['count'] }} stores</p>
                </a>
            @endforeach
        </div>
    </div>
</section>

<script>
function updateFilters() {
    const params = new URLSearchParams(window.location.search);
    
    // Get current filter values
    const category = document.querySelector('select[name="category"]').value;
    const sort = document.querySelector('select[name="sort"]').value;
    
    // Update URL parameters
    if (category) params.set('category', category);
    else params.delete('category');
    
    if (sort) params.set('sort', sort);
    else params.delete('sort');
    
    // Redirect with new parameters
    window.location.href = '{{ route("stores.index") }}?' + params.toString();
}

function setCountryFilter(country) {
    const params = new URLSearchParams(window.location.search);
    
    if (country) params.set('country', country);
    else params.delete('country');
    
    window.location.href = '{{ route("stores.index") }}?' + params.toString();
}
</script>
@endsection
