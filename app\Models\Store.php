<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Store extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'website_url',
        'affiliate_url',
        'affiliate_disclosure',
        'commission_rate',
        'affiliate_network',
        'categories',
        'country',
        'currency',
        'rating',
        'pros',
        'cons',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'categories' => 'array',
        'meta_keywords' => 'array',
        'commission_rate' => 'decimal:2',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Relationships
    public function coupons(): HasMany
    {
        return $this->hasMany(Coupon::class);
    }

    public function activeCoupons(): HasMany
    {
        return $this->hasMany(Coupon::class)->active();
    }

    public function affiliateClicks(): MorphMany
    {
        return $this->morphMany(AffiliateClick::class, 'clickable');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCountry(Builder $query, string $country): Builder
    {
        return $query->where('country', $country);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getMetaTitleAttribute($value): string
    {
        return $value ?: $this->name . ' Coupons & Deals';
    }

    public function getAffiliateUrlAttribute($value): string
    {
        return $value ?: $this->website_url;
    }

    // Methods
    public function getActiveCouponsCount(): int
    {
        return $this->activeCoupons()->count();
    }

    public function getAverageSuccessRate(): float
    {
        return $this->coupons()->where('is_active', true)->avg('success_rate') ?: 0;
    }

    public function getTotalClicks(): int
    {
        return $this->affiliateClicks()->count();
    }

    public function getClicksThisMonth(): int
    {
        return $this->affiliateClicks()
            ->whereMonth('clicked_at', now()->month)
            ->whereYear('clicked_at', now()->year)
            ->count();
    }

    public function hasValidCoupons(): bool
    {
        return $this->activeCoupons()->count() > 0;
    }
}
