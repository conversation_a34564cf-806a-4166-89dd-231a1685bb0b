@props([
    'url' => null,
    'skeleton' => true,
    'animation' => 'fade-in',
    'delay' => 0
])

<div 
    data-lazy-content
    @if($url) data-lazy-url="{{ $url }}" @endif
    class="{{ $animation }} {{ $attributes->get('class', '') }}"
    @if($delay) style="transition-delay: {{ $delay }}ms" @endif
    {{ $attributes->except(['class']) }}
>
    @if($skeleton && $url)
        <!-- Skeleton loading placeholder -->
        <div class="skeleton-card">
            <div class="skeleton skeleton-title"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text"></div>
        </div>
    @else
        {{ $slot }}
    @endif
</div>
