<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class BannerController extends Controller
{
    /**
     * Display a listing of banners.
     */
    public function index(Request $request): View
    {
        $query = Banner::query();

        // Apply filters
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'scheduled':
                    $query->where('is_active', true)
                          ->where('starts_at', '>', now());
                    break;
                case 'expired':
                    $query->where('is_active', true)
                          ->where('expires_at', '<', now());
                    break;
            }
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');

        if ($sortBy === 'sort_order') {
            $query->ordered();
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }

        $banners = $query->paginate(10)->withQueryString();

        return view('admin.banners.index', compact('banners'));
    }

    /**
     * Show the form for creating a new banner.
     */
    public function create(): View
    {
        return view('admin.banners.create');
    }

    /**
     * Store a newly created banner.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'image' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'link_url' => 'nullable|url',
            'link_text' => 'required|string|max:50',
            'link_target' => 'required|in:_self,_blank',
            'button_style' => 'required|in:primary,secondary,outline',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        $data = $request->except(['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        Banner::create($data);

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner created successfully!');
    }

    /**
     * Display the specified banner.
     */
    public function show(Banner $banner): View
    {
        return view('admin.banners.show', compact('banner'));
    }

    /**
     * Show the form for editing the specified banner.
     */
    public function edit(Banner $banner): View
    {
        return view('admin.banners.edit', compact('banner'));
    }

    /**
     * Update the specified banner.
     */
    public function update(Request $request, Banner $banner): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'link_url' => 'nullable|url',
            'link_text' => 'required|string|max:50',
            'link_target' => 'required|in:_self,_blank',
            'button_style' => 'required|in:primary,secondary,outline',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        $data = $request->except(['image']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($banner->image) {
                Storage::disk('public')->delete($banner->image);
            }
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        $banner->update($data);

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner updated successfully!');
    }

    /**
     * Remove the specified banner (soft delete).
     */
    public function destroy(Banner $banner): RedirectResponse
    {
        $banner->delete();

        // Clear cache
        $this->clearBannerCaches();

        return redirect()->route('admin.banners.index')
                        ->with('success', 'Banner moved to trash successfully!');
    }

    /**
     * Toggle banner active status.
     */
    public function toggleActive(Banner $banner): RedirectResponse
    {
        $banner->update(['is_active' => !$banner->is_active]);

        // Clear cache
        $this->clearBannerCaches();

        $status = $banner->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Banner {$status} successfully!");
    }

    /**
     * Display trashed banners.
     */
    public function trash(Request $request): View
    {
        $query = Banner::onlyTrashed();

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('deleted_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('deleted_at', '<=', $request->date_to);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'deleted_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $trashedBanners = $query->paginate(10)->withQueryString();

        return view('admin.banners.trash', compact('trashedBanners'));
    }

    /**
     * Restore a trashed banner.
     */
    public function restore($id): RedirectResponse
    {
        $banner = Banner::onlyTrashed()->findOrFail($id);
        $banner->restore();

        // Clear cache
        $this->clearBannerCaches();

        return back()->with('success', 'Banner restored successfully!');
    }

    /**
     * Permanently delete a banner.
     */
    public function forceDelete($id): RedirectResponse
    {
        $banner = Banner::onlyTrashed()->findOrFail($id);

        // Delete image
        if ($banner->image) {
            Storage::disk('public')->delete($banner->image);
        }

        $banner->forceDelete();

        // Clear cache
        $this->clearBannerCaches();

        return back()->with('success', 'Banner permanently deleted!');
    }

    /**
     * Handle image upload.
     */
    private function handleImageUpload($file): string
    {
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs('banners', $filename, 'public');
    }

    /**
     * Clear banner-related caches.
     */
    private function clearBannerCaches(): void
    {
        Cache::forget('homepage_banners');
        Cache::flush(); // Clear all cache for now
    }
}
