<div class="card-hover group text-center">
    <div class="p-6">
        <!-- Store Logo -->
        <div class="mb-4">
            @if($store->logo)
                <img src="{{ asset('storage/' . $store->logo) }}" 
                     alt="{{ $store->name }}" 
                     class="w-16 h-16 mx-auto rounded-lg object-cover group-hover:scale-110 transition-transform duration-300">
            @else
                <div class="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:from-blue-600 group-hover:to-purple-700 transition-colors duration-300">
                    <span class="text-white text-2xl font-bold">{{ substr($store->name, 0, 1) }}</span>
                </div>
            @endif
        </div>

        <!-- Store Name -->
        <h3 class="font-bold text-lg text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">
            <a href="{{ route('stores.show', $store->slug) }}">{{ $store->name }}</a>
        </h3>

        <!-- Store Description -->
        @if($store->description)
            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ Str::limit($store->description, 100) }}</p>
        @endif

        <!-- Store Stats -->
        <div class="space-y-2 mb-4">
            @if($store->activeCoupons->count() > 0)
                <div class="flex items-center justify-center space-x-1 text-sm text-green-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 5a3 3 0 015-2.236A3 3 0 0114.83 6H16a2 2 0 110 4h-5V9a1 1 0 10-2 0v1H4a2 2 0 110-4h1.17C5.06 5.687 5 5.35 5 5zm4 1V5a1 1 0 10-1 1h1zm3 0a1 1 0 10-1-1v1h1z" clip-rule="evenodd"></path>
                        <path d="M9 11H3v5a2 2 0 002 2h4v-7zM11 18h4a2 2 0 002-2v-5h-6v7z"></path>
                    </svg>
                    <span>{{ $store->activeCoupons->count() }} active {{ Str::plural('deal', $store->activeCoupons->count()) }}</span>
                </div>
            @endif

            @if($store->rating)
                <div class="flex items-center justify-center space-x-1">
                    <div class="flex items-center">
                        @for($i = 1; $i <= 5; $i++)
                            <svg class="w-4 h-4 {{ $i <= $store->rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                                 fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        @endfor
                    </div>
                    <span class="text-sm text-gray-600">{{ number_format($store->rating, 1) }}</span>
                </div>
            @endif

            @if($store->commission_rate)
                <div class="text-xs text-gray-500">
                    Up to {{ $store->commission_rate }}% cashback
                </div>
            @endif
        </div>

        <!-- Store Categories -->
        @if($store->categories && count($store->categories) > 0)
            <div class="flex flex-wrap justify-center gap-1 mb-4">
                @foreach(array_slice($store->categories, 0, 3) as $category)
                    <span class="badge-gray text-xs">{{ $category }}</span>
                @endforeach
                @if(count($store->categories) > 3)
                    <span class="badge-gray text-xs">+{{ count($store->categories) - 3 }} more</span>
                @endif
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="space-y-2">
            <a href="{{ route('stores.show', $store->slug) }}" 
               class="w-full btn-primary group-hover:bg-blue-700 transition-colors duration-200">
                View Deals
            </a>
            
            @if($store->website_url)
                <a href="{{ route('stores.visit', $store->slug) }}" 
                   target="_blank"
                   class="w-full btn-outline text-sm"
                   onclick="trackStoreVisit('{{ $store->slug }}')">
                    Visit Store
                </a>
            @endif
        </div>

        <!-- Store Features -->
        <div class="mt-4 flex justify-center space-x-4 text-xs text-gray-500">
            @if($store->is_featured)
                <span class="flex items-center space-x-1">
                    <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span>Featured</span>
                </span>
            @endif

            @if($store->affiliate_network)
                <span>{{ $store->affiliate_network }}</span>
            @endif

            @if($store->country !== 'US')
                <span>{{ $store->country }}</span>
            @endif
        </div>
    </div>
</div>

<script>
function trackStoreVisit(slug) {
    // Track store visit for analytics
    fetch(`/stores/${slug}/visit`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            utm_source: new URLSearchParams(window.location.search).get('utm_source'),
            utm_medium: new URLSearchParams(window.location.search).get('utm_medium'),
            utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
        })
    });
}
</script>
