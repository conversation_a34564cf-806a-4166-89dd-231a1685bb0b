<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Hero;
use App\Models\Banner;
use App\Models\Partner;
use App\Models\Category;
use App\Models\Blog;
use App\Models\Tag;

class CheckHomepageData extends Command
{
    protected $signature = 'check:homepage-data';
    protected $description = 'Check if homepage data exists in database';

    public function handle()
    {
        $this->info('🔍 Checking Homepage Data...');
        $this->newLine();

        // Check Heroes
        try {
            $heroesCount = Hero::count();
            $activeHeroesCount = Hero::active()->count();
            $this->info("✅ Heroes Table: {$heroesCount} total, {$activeHeroesCount} active");
            
            if ($activeHeroesCount > 0) {
                $heroes = Hero::active()->ordered()->limit(3)->get();
                foreach ($heroes as $hero) {
                    $this->line("   - {$hero->title} (Active: " . ($hero->is_active ? 'Yes' : 'No') . ")");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Heroes Table Error: " . $e->getMessage());
        }

        $this->newLine();

        // Check Banners
        try {
            $bannersCount = Banner::count();
            $activeBannersCount = Banner::active()->count();
            $this->info("✅ Banners Table: {$bannersCount} total, {$activeBannersCount} active");
            
            if ($activeBannersCount > 0) {
                $banners = Banner::active()->ordered()->limit(3)->get();
                foreach ($banners as $banner) {
                    $this->line("   - {$banner->title} (Active: " . ($banner->is_active ? 'Yes' : 'No') . ")");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Banners Table Error: " . $e->getMessage());
        }

        $this->newLine();

        // Check Partners
        try {
            $partnersCount = Partner::count();
            $activePartnersCount = Partner::active()->count();
            $this->info("✅ Partners Table: {$partnersCount} total, {$activePartnersCount} active");
        } catch (\Exception $e) {
            $this->error("❌ Partners Table Error: " . $e->getMessage());
        }

        $this->newLine();

        // Check Categories
        try {
            $categoriesCount = Category::count();
            $activeCategoriesCount = Category::active()->count();
            $this->info("✅ Categories Table: {$categoriesCount} total, {$activeCategoriesCount} active");
        } catch (\Exception $e) {
            $this->error("❌ Categories Table Error: " . $e->getMessage());
        }

        $this->newLine();

        // Check Blogs
        try {
            $blogsCount = Blog::count();
            $publishedBlogsCount = Blog::published()->count();
            $this->info("✅ Blogs Table: {$blogsCount} total, {$publishedBlogsCount} published");
        } catch (\Exception $e) {
            $this->error("❌ Blogs Table Error: " . $e->getMessage());
        }

        $this->newLine();

        // Check Tags
        try {
            $tagsCount = Tag::count();
            $activeTagsCount = Tag::active()->count();
            $this->info("✅ Tags Table: {$tagsCount} total, {$activeTagsCount} active");
        } catch (\Exception $e) {
            $this->error("❌ Tags Table Error: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎯 Recommendations:');
        
        if ($activeHeroesCount === 0) {
            $this->warn('⚠️  No active heroes found. Run: php artisan db:seed --class=HeroSeeder');
        }
        
        if ($activeBannersCount === 0) {
            $this->warn('⚠️  No active banners found. Run: php artisan db:seed --class=BannerSeeder');
        }
        
        if ($publishedBlogsCount === 0) {
            $this->warn('⚠️  No published blogs found. Run: php artisan db:seed --class=BlogDemoSeeder');
        }

        $this->newLine();
        $this->info('✨ To seed all demo data: php artisan db:seed --class=DemoDataSeeder');
    }
}
