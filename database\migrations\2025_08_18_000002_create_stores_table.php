<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stores', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('website_url');
            $table->string('affiliate_url')->nullable();
            $table->text('affiliate_disclosure')->nullable();
            $table->decimal('commission_rate', 5, 2)->nullable(); // e.g., 5.50 for 5.5%
            $table->string('affiliate_network')->nullable(); // e.g., 'ShareASale', 'CJ', 'Amazon'
            $table->json('categories')->nullable(); // Store categories
            $table->string('country', 2)->default('US'); // ISO country code
            $table->string('currency', 3)->default('USD'); // ISO currency code
            $table->decimal('rating', 3, 2)->nullable(); // e.g., 4.50
            $table->text('pros')->nullable();
            $table->text('cons')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('meta_keywords')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['is_active', 'is_featured']);
            $table->index(['sort_order']);
            $table->index(['country']);
            $table->fullText(['name', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stores');
    }
};
