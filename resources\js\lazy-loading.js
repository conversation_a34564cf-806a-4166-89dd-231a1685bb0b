/**
 * Lazy Loading Implementation
 * Handles lazy loading for images and content sections
 */

class LazyLoader {
    constructor(options = {}) {
        this.options = {
            imageSelector: 'img[data-src], img[loading="lazy"]',
            contentSelector: '[data-lazy-content]',
            rootMargin: '50px',
            threshold: 0.1,
            fadeInDuration: 300,
            ...options
        };

        this.imageObserver = null;
        this.contentObserver = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.setupImageObserver();
            this.setupContentObserver();
            this.observeElements();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
            this.loadAllContent();
        }
    }

    setupImageObserver() {
        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: this.options.rootMargin,
            threshold: this.options.threshold
        });
    }

    setupContentObserver() {
        this.contentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadContent(entry.target);
                    this.contentObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: this.options.rootMargin,
            threshold: this.options.threshold
        });
    }

    observeElements() {
        // Observe images
        const images = document.querySelectorAll(this.options.imageSelector);
        images.forEach(img => {
            if (this.imageObserver) {
                this.imageObserver.observe(img);
            }
        });

        // Observe content sections
        const contentSections = document.querySelectorAll(this.options.contentSelector);
        contentSections.forEach(section => {
            if (this.contentObserver) {
                this.contentObserver.observe(section);
            }
        });
    }

    loadImage(img) {
        // Handle data-src attribute
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }

        // Handle data-srcset attribute
        if (img.dataset.srcset) {
            img.srcset = img.dataset.srcset;
            img.removeAttribute('data-srcset');
        }

        // Add loading class for fade-in effect
        img.classList.add('lazy-loading');

        img.onload = () => {
            img.classList.remove('lazy-loading');
            img.classList.add('lazy-loaded');
            this.fadeIn(img);
        };

        img.onerror = () => {
            img.classList.remove('lazy-loading');
            img.classList.add('lazy-error');
            // Set fallback image if available
            if (img.dataset.fallback) {
                img.src = img.dataset.fallback;
            }
        };
    }

    loadContent(element) {
        element.classList.add('lazy-content-loading');

        // If content has data-lazy-url, fetch content via AJAX
        if (element.dataset.lazyUrl) {
            this.fetchContent(element);
        } else {
            // Just reveal the content with animation
            this.revealContent(element);
        }
    }

    async fetchContent(element) {
        try {
            const response = await fetch(element.dataset.lazyUrl);
            const content = await response.text();
            element.innerHTML = content;
            this.revealContent(element);
        } catch (error) {
            console.error('Failed to load lazy content:', error);
            element.classList.add('lazy-content-error');
            element.innerHTML = '<p class="text-gray-500">Failed to load content</p>';
        }
    }

    revealContent(element) {
        element.classList.remove('lazy-content-loading');
        element.classList.add('lazy-content-loaded');
        this.fadeIn(element);
    }

    fadeIn(element) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${this.options.fadeInDuration}ms ease-in-out`;
        
        // Force reflow
        element.offsetHeight;
        
        element.style.opacity = '1';
    }

    loadAllImages() {
        // Fallback for browsers without IntersectionObserver
        const images = document.querySelectorAll(this.options.imageSelector);
        images.forEach(img => this.loadImage(img));
    }

    loadAllContent() {
        // Fallback for browsers without IntersectionObserver
        const contentSections = document.querySelectorAll(this.options.contentSelector);
        contentSections.forEach(section => this.loadContent(section));
    }

    // Public method to add new elements to observe
    observe(element) {
        if (element.tagName === 'IMG' && this.imageObserver) {
            this.imageObserver.observe(element);
        } else if (element.dataset.lazyContent && this.contentObserver) {
            this.contentObserver.observe(element);
        }
    }

    // Public method to stop observing
    disconnect() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        if (this.contentObserver) {
            this.contentObserver.disconnect();
        }
    }
}

// CSS for lazy loading effects
const lazyLoadingCSS = `
    .lazy-loading {
        opacity: 0.5;
        filter: blur(2px);
        transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
    }

    .lazy-loaded {
        opacity: 1;
        filter: none;
    }

    .lazy-error {
        opacity: 0.7;
        filter: grayscale(100%);
    }

    .lazy-content-loading {
        opacity: 0.5;
        position: relative;
    }

    .lazy-content-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: lazy-spin 1s linear infinite;
    }

    .lazy-content-loaded {
        opacity: 1;
    }

    .lazy-content-error {
        opacity: 0.7;
        background-color: #fee;
        border: 1px solid #fcc;
        border-radius: 4px;
        padding: 1rem;
    }

    @keyframes lazy-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Skeleton loading animation */
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
    }

    @keyframes skeleton-loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = lazyLoadingCSS;
document.head.appendChild(style);

// Initialize lazy loader when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyLoader();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LazyLoader;
}
