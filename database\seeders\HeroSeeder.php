<?php

namespace Database\Seeders;

use App\Models\Hero;
use Illuminate\Database\Seeder;

class HeroSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $heroes = [
            [
                'title' => 'Discover Amazing Blog Content',
                'subtitle' => 'Welcome to Your Ultimate Blog Destination',
                'description' => 'Explore insightful articles, lifestyle tips, and expert advice to enhance your daily life and broaden your horizons.',
                'background_image' => 'heroes/hero-1.jpg',
                'background_color' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'text_color' => 'white',
                'text_position' => 'center',
                'primary_button_text' => 'Browse Articles',
                'primary_button_url' => '/blog',
                'primary_button_style' => 'primary',
                'secondary_button_text' => 'Learn More',
                'secondary_button_url' => '/about',
                'secondary_button_style' => 'outline',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Stay Informed, Stay Inspired',
                'subtitle' => 'Your Daily Dose of Knowledge',
                'description' => 'From technology trends to lifestyle hacks, we bring you the most engaging and informative content across multiple niches.',
                'background_image' => 'heroes/hero-2.jpg',
                'background_color' => 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'text_color' => 'white',
                'text_position' => 'left',
                'primary_button_text' => 'Explore Categories',
                'primary_button_url' => '/blog',
                'primary_button_style' => 'primary',
                'secondary_button_text' => 'Popular Tags',
                'secondary_button_url' => '/blog',
                'secondary_button_style' => 'outline',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Join Our Community',
                'subtitle' => 'Connect, Learn, Grow',
                'description' => 'Be part of a thriving community of readers, writers, and thinkers who share your passion for quality content and meaningful discussions.',
                'background_image' => 'heroes/hero-3.jpg',
                'background_color' => 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'text_color' => 'white',
                'text_position' => 'right',
                'primary_button_text' => 'Start Reading',
                'primary_button_url' => '/blog',
                'primary_button_style' => 'primary',
                'secondary_button_text' => 'About Us',
                'secondary_button_url' => '/about',
                'secondary_button_style' => 'outline',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Unlock Your Potential',
                'subtitle' => 'Transform Your Life Through Knowledge',
                'description' => 'Discover powerful insights, practical tips, and inspiring stories that will help you achieve your goals and live your best life.',
                'background_image' => 'heroes/hero-4.jpg',
                'background_color' => 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                'text_color' => 'dark',
                'text_position' => 'center',
                'primary_button_text' => 'Get Inspired',
                'primary_button_url' => '/blog',
                'primary_button_style' => 'primary',
                'secondary_button_text' => 'Featured Posts',
                'secondary_button_url' => '/blog?featured=1',
                'secondary_button_style' => 'outline',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'title' => 'Expert Insights & Trends',
                'subtitle' => 'Stay Ahead of the Curve',
                'description' => 'Get exclusive access to expert analysis, industry trends, and cutting-edge insights that keep you informed and ahead of the competition.',
                'background_image' => 'heroes/hero-5.jpg',
                'background_color' => 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                'text_color' => 'dark',
                'text_position' => 'left',
                'primary_button_text' => 'Read Expert Content',
                'primary_button_url' => '/blog',
                'primary_button_style' => 'primary',
                'secondary_button_text' => 'Latest Trends',
                'secondary_button_url' => '/blog?sort=latest',
                'secondary_button_style' => 'outline',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($heroes as $hero) {
            Hero::create($hero);
        }
    }
}
