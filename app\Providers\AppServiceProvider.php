<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Blade directives for responsive images
        \Blade::directive('responsiveImage', function ($expression) {
            return "<?php echo \App\Helpers\ResponsiveImageHelper::generate($expression); ?>";
        });

        \Blade::directive('simpleResponsiveImage', function ($expression) {
            return "<?php echo \App\Helpers\ResponsiveImageHelper::generateSimple($expression); ?>";
        });

        \Blade::directive('progressiveImage', function ($expression) {
            return "<?php echo \App\Helpers\ResponsiveImageHelper::generateProgressive($expression); ?>";
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema::defaultStringLength(191);
    }
}
