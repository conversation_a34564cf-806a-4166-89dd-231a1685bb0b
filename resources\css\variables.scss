// CSS Variables for BlogHub
// This file contains all the design tokens and variables used throughout the application

// Colors
:root {
    // Primary Colors
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;

    // Secondary Colors (Purple)
    --color-secondary-50: #faf5ff;
    --color-secondary-100: #f3e8ff;
    --color-secondary-200: #e9d5ff;
    --color-secondary-300: #d8b4fe;
    --color-secondary-400: #c084fc;
    --color-secondary-500: #a855f7;
    --color-secondary-600: #9333ea;
    --color-secondary-700: #7c3aed;
    --color-secondary-800: #6b21a8;
    --color-secondary-900: #581c87;

    // Gray Scale
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    // Status Colors
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;

    // Gradients
    --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary-500) 0%, var(--color-primary-500) 100%);
    --gradient-dark: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-900) 100%);

    // Typography
    --font-family-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

    // Font Sizes
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    // Line Heights
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    // Spacing
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    // Border Radius
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-base: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    // Shadows
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    // Z-Index
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;

    // Transitions
    --transition-fast: 150ms ease-in-out;
    --transition-base: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;

    // Breakpoints (for use in JavaScript)
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    // Component Specific
    --header-height: 4rem;
    --sidebar-width: 16rem;
    --content-max-width: 80rem;
    
    // Card styles
    --card-bg: #ffffff;
    --card-border: var(--color-gray-200);
    --card-shadow: var(--shadow-base);
    --card-radius: var(--radius-xl);
    
    // Button styles
    --btn-padding-x: var(--space-4);
    --btn-padding-y: var(--space-2);
    --btn-radius: var(--radius-lg);
    --btn-transition: var(--transition-fast);
    
    // Form styles
    --input-bg: #ffffff;
    --input-border: var(--color-gray-300);
    --input-border-focus: var(--color-primary-500);
    --input-padding-x: var(--space-3);
    --input-padding-y: var(--space-2);
    --input-radius: var(--radius-lg);
}

// Dark mode variables
@media (prefers-color-scheme: dark) {
    :root {
        --card-bg: var(--color-gray-800);
        --card-border: var(--color-gray-700);
        --input-bg: var(--color-gray-800);
        --input-border: var(--color-gray-600);
    }
}

// SCSS Variables (for use in SCSS files)
$primary-color: var(--color-primary-500);
$secondary-color: var(--color-secondary-500);
$gradient-primary: var(--gradient-primary);
$gradient-secondary: var(--gradient-secondary);

$font-sans: var(--font-family-sans);
$font-mono: var(--font-family-mono);

$transition-fast: var(--transition-fast);
$transition-base: var(--transition-base);
$transition-slow: var(--transition-slow);

$shadow-sm: var(--shadow-sm);
$shadow-base: var(--shadow-base);
$shadow-lg: var(--shadow-lg);

// Utility mixins
@mixin gradient-primary {
    background: var(--gradient-primary);
}

@mixin gradient-secondary {
    background: var(--gradient-secondary);
}

@mixin card-style {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--card-radius);
    box-shadow: var(--card-shadow);
}

@mixin button-primary {
    @include gradient-primary;
    color: white;
    padding: var(--btn-padding-y) var(--btn-padding-x);
    border-radius: var(--btn-radius);
    border: none;
    font-weight: 600;
    transition: var(--btn-transition);
    cursor: pointer;
    
    &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }
}

@mixin input-style {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--input-radius);
    padding: var(--input-padding-y) var(--input-padding-x);
    transition: var(--transition-fast);
    
    &:focus {
        outline: none;
        border-color: var(--input-border-focus);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
}
