<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Intervention\Image\Facades\Image;

class StoreController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of stores.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $country = $request->get('country');

        $query = Store::withCount(['coupons', 'activeCoupons']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($status === 'active') {
            $query->active();
        } elseif ($status === 'inactive') {
            $query->where('is_active', false);
        } elseif ($status === 'featured') {
            $query->featured();
        }

        if ($country) {
            $query->byCountry($country);
        }

        $stores = $query->latest()->paginate(20);
        $countries = Store::distinct()->pluck('country')->sort();

        return view('admin.stores.index', compact('stores', 'countries'));
    }

    /**
     * Show the form for creating a new store.
     */
    public function create(): View
    {
        $categories = Category::active()->pluck('name', 'slug');
        return view('admin.stores.create', compact('categories'));
    }

    /**
     * Store a newly created store.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'website_url' => 'required|url',
            'affiliate_url' => 'nullable|url',
            'affiliate_disclosure' => 'nullable|string',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'affiliate_network' => 'nullable|string|max:255',
            'categories' => 'nullable|array',
            'country' => 'required|string|size:2',
            'currency' => 'required|string|size:3',
            'rating' => 'nullable|numeric|min:0|max:5',
            'pros' => 'nullable|string',
            'cons' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['logo', 'meta_keywords', 'categories']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;
        $data['categories'] = $request->categories ? array_filter($request->categories) : null;
        $data['sort_order'] = $data['sort_order'] ?? 0;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $data['logo'] = $this->handleImageUpload($request->file('logo'));
        }

        $store = Store::create($data);

        // Clear relevant caches
        $this->clearStoreCaches();

        return redirect()->route('admin.stores.index')
                        ->with('success', 'Store created successfully!');
    }

    /**
     * Display the specified store.
     */
    public function show(Store $store): View
    {
        $store->load(['coupons' => function ($query) {
            $query->latest()->limit(10);
        }]);
        
        $store->loadCount(['coupons', 'activeCoupons', 'affiliateClicks']);
        
        return view('admin.stores.show', compact('store'));
    }

    /**
     * Show the form for editing the specified store.
     */
    public function edit(Store $store): View
    {
        $categories = Category::active()->pluck('name', 'slug');
        return view('admin.stores.edit', compact('store', 'categories'));
    }

    /**
     * Update the specified store.
     */
    public function update(Request $request, Store $store): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'website_url' => 'required|url',
            'affiliate_url' => 'nullable|url',
            'affiliate_disclosure' => 'nullable|string',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'affiliate_network' => 'nullable|string|max:255',
            'categories' => 'nullable|array',
            'country' => 'required|string|size:2',
            'currency' => 'required|string|size:3',
            'rating' => 'nullable|numeric|min:0|max:5',
            'pros' => 'nullable|string',
            'cons' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['logo', 'meta_keywords', 'categories']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;
        $data['categories'] = $request->categories ? array_filter($request->categories) : null;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($store->logo) {
                Storage::disk('public')->delete($store->logo);
            }
            $data['logo'] = $this->handleImageUpload($request->file('logo'));
        }

        $oldSlug = $store->slug;
        $store->update($data);

        // Clear relevant caches
        $this->clearStoreCaches($oldSlug, $store->slug);

        return redirect()->route('admin.stores.index')
                        ->with('success', 'Store updated successfully!');
    }

    /**
     * Remove the specified store.
     */
    public function destroy(Store $store): RedirectResponse
    {
        // Check if store has coupons
        if ($store->coupons()->count() > 0) {
            return back()->with('error', 'Cannot delete store with associated coupons. Please delete coupons first.');
        }

        $slug = $store->slug;
        
        // Delete logo
        if ($store->logo) {
            Storage::disk('public')->delete($store->logo);
        }

        $store->delete();

        // Clear relevant caches
        $this->clearStoreCaches($slug);

        return redirect()->route('admin.stores.index')
                        ->with('success', 'Store deleted successfully!');
    }

    /**
     * Toggle store active status.
     */
    public function toggleActive(Store $store): RedirectResponse
    {
        $store->update(['is_active' => !$store->is_active]);
        
        $this->clearStoreCaches($store->slug);

        $status = $store->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Store {$status} successfully!");
    }

    /**
     * Toggle store featured status.
     */
    public function toggleFeatured(Store $store): RedirectResponse
    {
        $store->update(['is_featured' => !$store->is_featured]);
        
        $this->clearStoreCaches($store->slug);

        $status = $store->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Store {$status} successfully!");
    }

    /**
     * Handle image upload and optimization.
     */
    private function handleImageUpload($file): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = 'stores/' . $filename;

        // Resize and optimize logo
        $image = Image::make($file)
                     ->resize(200, 200, function ($constraint) {
                         $constraint->aspectRatio();
                         $constraint->upsize();
                     })
                     ->encode('png', 90);

        Storage::disk('public')->put($path, $image);

        return $path;
    }

    /**
     * Clear store-related caches.
     */
    private function clearStoreCaches(string $oldSlug = null, string $newSlug = null): void
    {
        // Clear general caches
        Cache::forget('homepage_data');
        
        // Clear specific store caches
        if ($oldSlug) {
            for ($i = 1; $i <= 5; $i++) {
                Cache::forget("store_{$oldSlug}_all_{$i}");
                Cache::forget("store_{$oldSlug}_coupons_{$i}");
                Cache::forget("store_{$oldSlug}_deals_{$i}");
            }
        }
        
        if ($newSlug && $newSlug !== $oldSlug) {
            for ($i = 1; $i <= 5; $i++) {
                Cache::forget("store_{$newSlug}_all_{$i}");
                Cache::forget("store_{$newSlug}_coupons_{$i}");
                Cache::forget("store_{$newSlug}_deals_{$i}");
            }
        }

        // Clear stores listing caches
        for ($i = 1; $i <= 10; $i++) {
            Cache::forget("stores_popular_{$i}");
            Cache::forget("stores_name_{$i}");
            Cache::forget("stores_newest_{$i}");
        }

        // Clear response cache if available
        if (class_exists('\Spatie\ResponseCache\Facades\ResponseCache')) {
            \Spatie\ResponseCache\Facades\ResponseCache::clear();
        }
    }
}
