<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Intervention\Image\Facades\Image;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of categories.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $parent = $request->get('parent');

        $query = Category::with(['parent', 'children'])
                        ->withCount(['blogs', 'coupons']);

        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }

        if ($parent === 'only') {
            $query->parent();
        } elseif ($parent === 'child') {
            $query->child();
        }

        $categories = $query->ordered()->paginate(20);
        $parentCategories = Category::active()->parent()->pluck('name', 'id');

        return view('admin.categories.index', compact('categories', 'parentCategories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create(): View
    {
        $parentCategories = Category::active()->parent()->pluck('name', 'id');
        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'parent_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['image', 'meta_keywords']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;
        $data['sort_order'] = $data['sort_order'] ?? 0;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        $category = Category::create($data);

        // Clear relevant caches
        $this->clearCategoryCaches();

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category created successfully!');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category): View
    {
        $category->load(['parent', 'children', 'blogs', 'coupons']);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category): View
    {
        $parentCategories = Category::active()
                                  ->parent()
                                  ->where('id', '!=', $category->id)
                                  ->pluck('name', 'id');
        
        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, Category $category): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'parent_id' => 'nullable|exists:categories,id|not_in:' . $category->id,
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['image', 'meta_keywords']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }
            $data['image'] = $this->handleImageUpload($request->file('image'));
        }

        $oldSlug = $category->slug;
        $category->update($data);

        // Clear relevant caches
        $this->clearCategoryCaches($oldSlug, $category->slug);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category updated successfully!');
    }

    /**
     * Remove the specified category.
     */
    public function destroy(Category $category): RedirectResponse
    {
        // Check if category has children
        if ($category->children()->count() > 0) {
            return back()->with('error', 'Cannot delete category with subcategories. Please delete or move subcategories first.');
        }

        // Check if category has associated content
        if ($category->blogs()->count() > 0 || $category->coupons()->count() > 0) {
            return back()->with('error', 'Cannot delete category with associated blogs or coupons. Please reassign content first.');
        }

        $slug = $category->slug;
        
        // Delete image
        if ($category->image) {
            Storage::disk('public')->delete($category->image);
        }

        $category->delete();

        // Clear relevant caches
        $this->clearCategoryCaches($slug);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category deleted successfully!');
    }

    /**
     * Toggle category active status.
     */
    public function toggleActive(Category $category): RedirectResponse
    {
        $category->update(['is_active' => !$category->is_active]);
        
        $this->clearCategoryCaches($category->slug);

        $status = $category->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Category {$status} successfully!");
    }

    /**
     * Update category sort order.
     */
    public function updateOrder(Request $request): RedirectResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                   ->update(['sort_order' => $categoryData['sort_order']]);
        }

        $this->clearCategoryCaches();

        return back()->with('success', 'Category order updated successfully!');
    }

    /**
     * Handle image upload and optimization.
     */
    private function handleImageUpload($file): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = 'categories/' . $filename;

        // Resize and optimize image
        $image = Image::make($file)
                     ->resize(400, 300, function ($constraint) {
                         $constraint->aspectRatio();
                         $constraint->upsize();
                     })
                     ->encode('jpg', 85);

        Storage::disk('public')->put($path, $image);

        return $path;
    }

    /**
     * Clear category-related caches.
     */
    private function clearCategoryCaches(string $oldSlug = null, string $newSlug = null): void
    {
        // Clear general caches
        Cache::forget('homepage_data');
        Cache::forget('categories_index');

        // Clear specific category caches
        if ($oldSlug) {
            for ($i = 1; $i <= 5; $i++) {
                Cache::forget("category_{$oldSlug}_all_{$i}");
                Cache::forget("category_{$oldSlug}_blogs_{$i}");
                Cache::forget("category_{$oldSlug}_coupons_{$i}");
            }
        }

        if ($newSlug && $newSlug !== $oldSlug) {
            for ($i = 1; $i <= 5; $i++) {
                Cache::forget("category_{$newSlug}_all_{$i}");
                Cache::forget("category_{$newSlug}_blogs_{$i}");
                Cache::forget("category_{$newSlug}_coupons_{$i}");
            }
        }

        // Clear response cache if available
        if (class_exists('\Spatie\ResponseCache\Facades\ResponseCache')) {
            \Spatie\ResponseCache\Facades\ResponseCache::clear();
        }
    }
}
