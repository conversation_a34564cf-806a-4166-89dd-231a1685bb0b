@props([
    'images' => [],
    'alt' => '',
    'class' => '',
    'sizes' => [
        '(max-width: 640px) 100vw',
        '(max-width: 1024px) 50vw',
        '33vw'
    ],
    'lazy' => true,
    'webp' => true,
    'type' => 'picture', // 'picture', 'simple', 'progressive'
    'fallback' => null
])

@php
    use App\Helpers\ResponsiveImageHelper;

    // If images is a string (single image path), convert to expected format
    if (is_string($images)) {
        $images = ['original' => ['original' => $images, 'webp' => null]];
    }

    $options = [
        'class' => $class,
        'loading' => $lazy ? 'lazy' : 'eager',
        'sizes' => $sizes,
        'webp' => $webp,
        'fallback' => $fallback
    ];

    // Generate responsive image HTML based on type
    switch ($type) {
        case 'simple':
            $imageHtml = ResponsiveImageHelper::generateSimple($images, $alt, $options);
            break;
        case 'progressive':
            $imageHtml = ResponsiveImageHelper::generateProgressive($images, $alt, $options);
            break;
        case 'picture':
        default:
            $imageHtml = ResponsiveImageHelper::generate($images, $alt, $options);
            break;
    }
@endphp

{!! $imageHtml !!}
