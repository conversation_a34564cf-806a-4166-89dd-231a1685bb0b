@echo off
echo Starting development servers for port 8000...

echo.
echo Stopping any existing processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im php.exe >nul 2>&1

echo.
echo Clearing Laravel caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo.
echo Starting Vite dev server...
start "Vite Dev Server" cmd /k "npm run dev"

echo.
echo Waiting for Vite to start...
timeout /t 5 /nobreak >nul

echo.
echo Starting Laravel dev server on port 8000...
start "Laravel Dev Server" cmd /k "php artisan serve --host=127.0.0.1 --port=8000"

echo.
echo Development servers are starting...
echo Vite: http://127.0.0.1:5173
echo Laravel: http://127.0.0.1:8000
echo.
echo Your Swiper sliders should now work properly!
echo.
echo Press any key to exit...
pause >nul
