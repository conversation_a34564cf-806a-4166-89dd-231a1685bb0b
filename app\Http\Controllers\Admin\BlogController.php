<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Intervention\Image\Facades\Image;

class BlogController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of blogs.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $category = $request->get('category');
        $status = $request->get('status');

        $query = Blog::with(['primaryCategory', 'author']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        if ($category) {
            $query->where('category_id', $category);
        }

        if ($status === 'published') {
            $query->published();
        } elseif ($status === 'draft') {
            $query->where('is_published', false);
        } elseif ($status === 'featured') {
            $query->featured();
        }

        $blogs = $query->latest()->paginate(20);
        $categories = Category::active()->pluck('name', 'id');

        return view('admin.blogs.index', compact('blogs', 'categories'));
    }

    /**
     * Show the form for creating a new blog.
     */
    public function create(): View
    {
        $categories = Category::active()->pluck('name', 'id');
        return view('admin.blogs.create', compact('categories'));
    }

    /**
     * Store a newly created blog.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'canonical_url' => 'nullable|url',
            'category_id' => 'nullable|exists:categories,id',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        $data = $request->except(['featured_image', 'meta_keywords']);
        $data['author_id'] = auth()->id();
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $this->handleImageUpload($request->file('featured_image'));
        }

        // Set published_at if not provided but is_published is true
        if ($data['is_published'] && !$data['published_at']) {
            $data['published_at'] = now();
        }

        $blog = Blog::create($data);

        // Clear relevant caches
        $this->clearBlogCaches();

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog created successfully!');
    }

    /**
     * Display the specified blog.
     */
    public function show(Blog $blog): View
    {
        $blog->load(['primaryCategory', 'categories', 'author']);
        return view('admin.blogs.show', compact('blog'));
    }

    /**
     * Show the form for editing the specified blog.
     */
    public function edit(Blog $blog): View
    {
        $categories = Category::active()->pluck('name', 'id');
        return view('admin.blogs.edit', compact('blog', 'categories'));
    }

    /**
     * Update the specified blog.
     */
    public function update(Request $request, Blog $blog): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'canonical_url' => 'nullable|url',
            'category_id' => 'nullable|exists:categories,id',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        $data = $request->except(['featured_image', 'meta_keywords']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($blog->featured_image) {
                Storage::disk('public')->delete($blog->featured_image);
            }
            $data['featured_image'] = $this->handleImageUpload($request->file('featured_image'));
        }

        // Set published_at if not provided but is_published is true
        if ($data['is_published'] && !$data['published_at']) {
            $data['published_at'] = now();
        }

        $oldSlug = $blog->slug;
        $blog->update($data);

        // Clear relevant caches
        $this->clearBlogCaches($oldSlug, $blog->slug);

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog updated successfully!');
    }

    /**
     * Remove the specified blog.
     */
    public function destroy(Blog $blog): RedirectResponse
    {
        $slug = $blog->slug;
        
        // Delete featured image
        if ($blog->featured_image) {
            Storage::disk('public')->delete($blog->featured_image);
        }

        $blog->delete();

        // Clear relevant caches
        $this->clearBlogCaches($slug);

        return redirect()->route('admin.blogs.index')
                        ->with('success', 'Blog deleted successfully!');
    }

    /**
     * Toggle blog publication status.
     */
    public function togglePublished(Blog $blog): RedirectResponse
    {
        $blog->update([
            'is_published' => !$blog->is_published,
            'published_at' => !$blog->is_published ? now() : $blog->published_at,
        ]);

        $this->clearBlogCaches($blog->slug);

        $status = $blog->is_published ? 'published' : 'unpublished';
        return back()->with('success', "Blog {$status} successfully!");
    }

    /**
     * Toggle blog featured status.
     */
    public function toggleFeatured(Blog $blog): RedirectResponse
    {
        $blog->update(['is_featured' => !$blog->is_featured]);
        
        $this->clearBlogCaches($blog->slug);

        $status = $blog->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Blog {$status} successfully!");
    }

    /**
     * Handle image upload and optimization.
     */
    private function handleImageUpload($file): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = 'blogs/' . $filename;

        // Resize and optimize image
        $image = Image::make($file)
                     ->resize(1200, 800, function ($constraint) {
                         $constraint->aspectRatio();
                         $constraint->upsize();
                     })
                     ->encode('jpg', 85);

        Storage::disk('public')->put($path, $image);

        return $path;
    }

    /**
     * Clear blog-related caches.
     */
    private function clearBlogCaches(string $oldSlug = null, string $newSlug = null): void
    {
        // Clear general blog caches
        Cache::forget('homepage_data');
        
        // Clear specific blog caches
        if ($oldSlug) {
            Cache::forget("blog_{$oldSlug}");
            Cache::forget("related_blogs_{$oldSlug}");
        }
        
        if ($newSlug && $newSlug !== $oldSlug) {
            Cache::forget("blog_{$newSlug}");
            Cache::forget("related_blogs_{$newSlug}");
        }

        // Clear paginated blog caches (rough approach)
        for ($i = 1; $i <= 10; $i++) {
            Cache::forget("blogs_page_{$i}");
        }

        // Clear response cache if available
        if (class_exists('\Spatie\ResponseCache\Facades\ResponseCache')) {
            \Spatie\ResponseCache\Facades\ResponseCache::clear();
        }
    }
}
