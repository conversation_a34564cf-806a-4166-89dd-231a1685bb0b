<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'code',
        'type',
        'discount_type',
        'discount_value',
        'currency',
        'minimum_order',
        'affiliate_url',
        'store_id',
        'category_id',
        'starts_at',
        'expires_at',
        'is_exclusive',
        'is_verified',
        'is_active',
        'is_featured',
        'usage_count',
        'success_count',
        'success_rate',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'terms_conditions',
        'sort_order',
    ];

    protected $casts = [
        'meta_keywords' => 'array',
        'discount_value' => 'decimal:2',
        'minimum_order' => 'decimal:2',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_exclusive' => 'boolean',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'usage_count' => 'integer',
        'success_count' => 'integer',
        'success_rate' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Relationships
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function affiliateClicks(): MorphMany
    {
        return $this->morphMany(AffiliateClick::class, 'clickable');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }

    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    public function scopeExclusive(Builder $query): Builder
    {
        return $query->where('is_exclusive', true);
    }

    public function scopeVerified(Builder $query): Builder
    {
        return $query->where('is_verified', true);
    }

    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeByStore(Builder $query, $storeSlug): Builder
    {
        return $query->whereHas('store', function ($q) use ($storeSlug) {
            $q->where('slug', $storeSlug);
        });
    }

    public function scopeByCategory(Builder $query, $categorySlug): Builder
    {
        return $query->whereHas('category', function ($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('is_featured', 'desc')
                    ->orderBy('sort_order')
                    ->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getIsValidAttribute(): bool
    {
        return $this->is_active && !$this->is_expired;
    }

    public function getExpiresInDaysAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }
        
        return max(0, now()->diffInDays($this->expires_at, false));
    }

    public function getDiscountDisplayAttribute(): string
    {
        if (!$this->discount_value) {
            return '';
        }

        switch ($this->discount_type) {
            case 'percentage':
                return $this->discount_value . '% OFF';
            case 'fixed':
                return $this->currency . $this->discount_value . ' OFF';
            case 'free_shipping':
                return 'FREE SHIPPING';
            case 'bogo':
                return 'Buy One Get One';
            default:
                return '';
        }
    }

    public function getMetaTitleAttribute($value): string
    {
        return $value ?: $this->title . ' - ' . $this->store->name;
    }

    // Methods
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    public function incrementSuccess(): void
    {
        $this->increment('success_count');
        $this->updateSuccessRate();
    }

    public function updateSuccessRate(): void
    {
        if ($this->usage_count > 0) {
            $this->success_rate = ($this->success_count / $this->usage_count) * 100;
            $this->save();
        }
    }

    public function getTimeRemaining(): ?string
    {
        if (!$this->expires_at) {
            return null;
        }

        $diff = now()->diff($this->expires_at);
        
        if ($diff->days > 0) {
            return $diff->days . ' days';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hours';
        } else {
            return $diff->i . ' minutes';
        }
    }
}
