<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;

class ImageService
{
    protected $manager;
    
    public function __construct()
    {
        $this->manager = new ImageManager(['driver' => 'gd']);
    }

    /**
     * Process and store an uploaded image with WebP conversion
     */
    public function processAndStore(UploadedFile $file, string $directory = 'images', array $sizes = []): array
    {
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $timestamp = time();
        $baseName = $originalName . '_' . $timestamp;
        
        // Create the image instance
        $image = $this->manager->make($file);
        
        $results = [];
        
        // Default sizes if none provided
        if (empty($sizes)) {
            $sizes = [
                'original' => null,
                'large' => 1200,
                'medium' => 800,
                'small' => 400,
                'thumbnail' => 150
            ];
        }
        
        foreach ($sizes as $sizeName => $width) {
            $processedImage = clone $image;
            
            // Resize if width is specified
            if ($width) {
                $processedImage->resize($width, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }
            
            // Optimize the image
            $processedImage->sharpen(10);
            
            // Save original format
            $originalPath = $directory . '/' . $baseName . '_' . $sizeName . '.' . $file->getClientOriginalExtension();
            Storage::disk('public')->put($originalPath, $processedImage->encode($file->getClientOriginalExtension(), 85));
            
            // Save WebP format
            $webpPath = $directory . '/' . $baseName . '_' . $sizeName . '.webp';
            Storage::disk('public')->put($webpPath, $processedImage->encode('webp', 85));
            
            $results[$sizeName] = [
                'original' => $originalPath,
                'webp' => $webpPath,
                'width' => $processedImage->width(),
                'height' => $processedImage->height()
            ];
        }
        
        return $results;
    }

    /**
     * Generate responsive image HTML with WebP support
     */
    public function generateResponsiveImage(array $imagePaths, string $alt = '', string $class = '', array $sizes = []): string
    {
        if (empty($imagePaths)) {
            return '';
        }
        
        // Default sizes for responsive images
        if (empty($sizes)) {
            $sizes = [
                'small' => '(max-width: 640px) 100vw',
                'medium' => '(max-width: 1024px) 50vw',
                'large' => '33vw'
            ];
        }
        
        // Build srcset for WebP
        $webpSrcset = [];
        $originalSrcset = [];
        
        foreach ($imagePaths as $sizeName => $paths) {
            if (isset($paths['webp']) && isset($paths['width'])) {
                $webpSrcset[] = asset('storage/' . $paths['webp']) . ' ' . $paths['width'] . 'w';
            }
            if (isset($paths['original']) && isset($paths['width'])) {
                $originalSrcset[] = asset('storage/' . $paths['original']) . ' ' . $paths['width'] . 'w';
            }
        }
        
        // Get fallback image (largest available)
        $fallbackImage = end($imagePaths);
        $fallbackSrc = asset('storage/' . $fallbackImage['original']);
        
        // Build sizes attribute
        $sizesAttr = implode(', ', $sizes);
        
        $html = '<picture>';
        
        // WebP source
        if (!empty($webpSrcset)) {
            $html .= '<source type="image/webp" srcset="' . implode(', ', $webpSrcset) . '" sizes="' . $sizesAttr . '">';
        }
        
        // Original format source
        if (!empty($originalSrcset)) {
            $html .= '<source srcset="' . implode(', ', $originalSrcset) . '" sizes="' . $sizesAttr . '">';
        }
        
        // Fallback img tag
        $html .= '<img src="' . $fallbackSrc . '" alt="' . htmlspecialchars($alt) . '" class="' . $class . '" loading="lazy">';
        
        $html .= '</picture>';
        
        return $html;
    }

    /**
     * Delete all versions of an image
     */
    public function deleteImage(array $imagePaths): bool
    {
        $success = true;
        
        foreach ($imagePaths as $paths) {
            if (isset($paths['original']) && Storage::disk('public')->exists($paths['original'])) {
                $success = Storage::disk('public')->delete($paths['original']) && $success;
            }
            if (isset($paths['webp']) && Storage::disk('public')->exists($paths['webp'])) {
                $success = Storage::disk('public')->delete($paths['webp']) && $success;
            }
        }
        
        return $success;
    }

    /**
     * Get optimized image URL with WebP support detection
     */
    public function getOptimizedUrl(array $imagePaths, string $size = 'medium'): string
    {
        if (!isset($imagePaths[$size])) {
            $size = array_key_first($imagePaths);
        }
        
        $paths = $imagePaths[$size];
        
        // Return WebP if available (client-side detection would be needed for full support)
        if (isset($paths['webp'])) {
            return asset('storage/' . $paths['webp']);
        }
        
        return asset('storage/' . $paths['original']);
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, int $maxSize = 5120): array
    {
        $errors = [];
        
        // Check file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.';
        }
        
        // Check file size (in KB)
        if ($file->getSize() > $maxSize * 1024) {
            $errors[] = 'File size must be less than ' . $maxSize . 'KB.';
        }
        
        // Check image dimensions
        try {
            $image = $this->manager->make($file);
            if ($image->width() > 4000 || $image->height() > 4000) {
                $errors[] = 'Image dimensions must be less than 4000x4000 pixels.';
            }
        } catch (\Exception $e) {
            $errors[] = 'Invalid image file.';
        }
        
        return $errors;
    }
}
