<div class="card-hover group">
    <div class="p-6">
        <!-- Store Header -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
                @if($coupon->store->logo)
                    <img src="{{ asset('storage/' . $coupon->store->logo) }}" 
                         alt="{{ $coupon->store->name }}" 
                         class="w-12 h-12 rounded-lg object-cover">
                @else
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">{{ substr($coupon->store->name, 0, 1) }}</span>
                    </div>
                @endif
                <div>
                    <h3 class="font-semibold text-gray-900">{{ $coupon->store->name }}</h3>
                    @if($coupon->category)
                        <p class="text-sm text-gray-500">{{ $coupon->category->name }}</p>
                    @endif
                </div>
            </div>
            
            <!-- Discount Badge -->
            @if($coupon->discount_display)
                <span class="badge {{ $coupon->type === 'coupon' ? 'badge-danger' : ($coupon->type === 'deal' ? 'badge-success' : 'badge-warning') }}">
                    {{ $coupon->discount_display }}
                </span>
            @endif
        </div>

        <!-- Coupon Title & Description -->
        <h4 class="font-semibold text-lg text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">
            {{ $coupon->title }}
        </h4>
        <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $coupon->description }}</p>

        <!-- Coupon Details -->
        <div class="space-y-2 mb-4">
            @if($coupon->code)
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="text-sm font-medium text-gray-700">Coupon Code:</span>
                    <div class="flex items-center space-x-2">
                        <code class="coupon-code bg-white px-3 py-1 rounded border text-sm font-mono" 
                              data-code="{{ $coupon->code }}" 
                              data-url="{{ route('coupons.code', $coupon->slug) }}">
                            {{ $coupon->code }}
                        </code>
                        <button class="copy-code-btn text-blue-600 hover:text-blue-700 text-sm font-medium"
                                data-code="{{ $coupon->code }}">
                            Copy
                        </button>
                    </div>
                </div>
            @endif

            @if($coupon->minimum_order)
                <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Minimum order: {{ $coupon->currency }}{{ number_format($coupon->minimum_order, 2) }}
                </div>
            @endif

            @if($coupon->expires_at)
                <div class="flex items-center text-sm {{ $coupon->expires_in_days <= 3 ? 'text-red-600' : 'text-gray-600' }}">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    @if($coupon->expires_in_days > 0)
                        Expires in {{ $coupon->expires_in_days }} {{ Str::plural('day', $coupon->expires_in_days) }}
                    @else
                        Expires today
                    @endif
                </div>
            @endif
        </div>

        <!-- Badges -->
        <div class="flex flex-wrap gap-2 mb-4">
            @if($coupon->is_verified)
                <span class="badge-success">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Verified
                </span>
            @endif
            
            @if($coupon->is_exclusive)
                <span class="badge-warning">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    Exclusive
                </span>
            @endif

            @if($coupon->success_rate > 80)
                <span class="badge-success">{{ number_format($coupon->success_rate, 0) }}% Success</span>
            @elseif($coupon->success_rate > 60)
                <span class="badge-warning">{{ number_format($coupon->success_rate, 0) }}% Success</span>
            @endif
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3">
            <a href="{{ route('coupons.visit', $coupon->slug) }}" 
               target="_blank"
               class="flex-1 btn-primary text-center group-hover:bg-blue-700 transition-colors duration-200"
               onclick="trackCouponClick('{{ $coupon->slug }}')">
                @if($coupon->code)
                    Get Code & Visit Store
                @else
                    Get Deal
                @endif
            </a>
            <a href="{{ route('coupons.show', $coupon->slug) }}" 
               class="btn-outline px-4">
                Details
            </a>
        </div>

        <!-- Usage Stats -->
        @if($coupon->usage_count > 0)
            <div class="mt-3 text-center">
                <span class="text-xs text-gray-500">
                    Used by {{ number_format($coupon->usage_count) }} {{ Str::plural('person', $coupon->usage_count) }}
                </span>
            </div>
        @endif
    </div>
</div>

<script>
function trackCouponClick(slug) {
    // Track coupon click for analytics
    fetch(`/coupons/${slug}/visit`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            utm_source: new URLSearchParams(window.location.search).get('utm_source'),
            utm_medium: new URLSearchParams(window.location.search).get('utm_medium'),
            utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
        })
    });
}

// Copy code functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.copy-code-btn').forEach(button => {
        button.addEventListener('click', function() {
            const code = this.getAttribute('data-code');
            navigator.clipboard.writeText(code).then(() => {
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = 'Copy';
                }, 2000);
            });
        });
    });
});
</script>
