<?php

namespace App\Http\Controllers;

use App\Models\Coupon;
use App\Models\Store;
use App\Models\Category;
use App\Models\AffiliateClick;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class CouponController extends Controller
{
    /**
     * Show all coupons.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $store = $request->get('store');
        $category = $request->get('category');
        $type = $request->get('type'); // coupon, deal, cashback, freebie
        $sort = $request->get('sort', 'newest'); // newest, popular, expiring
        $page = $request->get('page', 1);

        $cacheKey = 'coupons_' . md5($search . $store . $category . $type . $sort . $page);

        $data = Cache::remember($cacheKey, 1800, function () use ($search, $store, $category, $type, $sort) {
            $query = Coupon::active()->with(['store', 'category']);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($store) {
                $query->byStore($store);
            }

            if ($category) {
                $query->byCategory($category);
            }

            if ($type) {
                $query->byType($type);
            }

            switch ($sort) {
                case 'popular':
                    $query->orderBy('usage_count', 'desc');
                    break;
                case 'expiring':
                    $query->whereNotNull('expires_at')
                          ->orderBy('expires_at', 'asc');
                    break;
                case 'newest':
                default:
                    $query->latest();
                    break;
            }

            $coupons = $query->paginate(24);

            // Featured coupons
            $featuredCoupons = Coupon::active()
                                   ->featured()
                                   ->with(['store', 'category'])
                                   ->limit(8)
                                   ->get();

            // Expiring soon coupons
            $expiringSoon = Coupon::active()
                                ->whereNotNull('expires_at')
                                ->where('expires_at', '>', now())
                                ->where('expires_at', '<=', now()->addDays(7))
                                ->with(['store', 'category'])
                                ->orderBy('expires_at')
                                ->limit(6)
                                ->get();

            // Filter options
            $stores = Store::active()
                          ->whereHas('activeCoupons')
                          ->orderBy('name')
                          ->pluck('name', 'slug');

            $categories = Category::active()
                                ->whereHas('coupons', function ($q) {
                                    $q->active();
                                })
                                ->orderBy('name')
                                ->pluck('name', 'slug');

            return compact('coupons', 'featuredCoupons', 'expiringSoon', 'stores', 'categories');
        });

        $seoData = [
            'title' => 'Coupons & Deals - Save Money with Verified Discount Codes',
            'description' => 'Find the latest coupons, promo codes, and deals. Save money with verified discount codes from top brands.',
            'canonical' => url('/coupons'),
        ];

        return view('coupons.index', array_merge($data, compact('seoData')));
    }

    /**
     * Show a specific coupon.
     */
    public function show(string $slug): View
    {
        $coupon = Cache::remember("coupon_{$slug}", 1800, function () use ($slug) {
            return Coupon::active()
                        ->with(['store', 'category'])
                        ->where('slug', $slug)
                        ->firstOrFail();
        });

        // Get related coupons
        $relatedCoupons = Cache::remember("related_coupons_{$slug}", 1800, function () use ($coupon) {
            return Coupon::active()
                        ->where('id', '!=', $coupon->id)
                        ->where(function ($query) use ($coupon) {
                            $query->where('store_id', $coupon->store_id)
                                  ->orWhere('category_id', $coupon->category_id);
                        })
                        ->with(['store', 'category'])
                        ->limit(6)
                        ->get();
        });

        $seoData = [
            'title' => $coupon->meta_title,
            'description' => $coupon->meta_description ?: $coupon->description,
            'keywords' => $coupon->meta_keywords,
            'canonical' => url()->current(),
            'type' => 'product',
            'offers' => [
                '@type' => 'Offer',
                'price' => $coupon->discount_value,
                'priceCurrency' => $coupon->currency,
                'availability' => $coupon->is_valid ? 'InStock' : 'OutOfStock',
                'validFrom' => $coupon->starts_at?->toISOString(),
                'validThrough' => $coupon->expires_at?->toISOString(),
                'seller' => [
                    '@type' => 'Organization',
                    'name' => $coupon->store->name,
                ],
            ],
        ];

        return view('coupons.show', compact('coupon', 'relatedCoupons', 'seoData'));
    }

    /**
     * Redirect to coupon's affiliate URL and track the click.
     */
    public function visit(string $slug, Request $request): RedirectResponse
    {
        $coupon = Coupon::active()->where('slug', $slug)->firstOrFail();

        // Track the affiliate click
        AffiliateClick::track($coupon, [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'utm_source' => $request->get('utm_source'),
            'utm_medium' => $request->get('utm_medium'),
            'utm_campaign' => $request->get('utm_campaign'),
            'device_type' => $this->getDeviceType($request->userAgent()),
        ]);

        // Increment usage count
        $coupon->incrementUsage();

        // Add affiliate disclosure parameters if needed
        $affiliateUrl = $coupon->affiliate_url;
        if ($request->has('utm_source')) {
            $separator = strpos($affiliateUrl, '?') !== false ? '&' : '?';
            $affiliateUrl .= $separator . http_build_query($request->only(['utm_source', 'utm_medium', 'utm_campaign']));
        }

        return redirect()->away($affiliateUrl);
    }

    /**
     * Mark coupon as successful (AJAX).
     */
    public function markSuccess(string $slug, Request $request): JsonResponse
    {
        $coupon = Coupon::active()->where('slug', $slug)->firstOrFail();
        
        $coupon->incrementSuccess();

        return response()->json([
            'success' => true,
            'message' => 'Thank you for confirming this coupon worked!',
            'success_rate' => $coupon->fresh()->success_rate,
        ]);
    }

    /**
     * Get coupon code (AJAX) - reveals the code and tracks usage.
     */
    public function getCode(string $slug, Request $request): JsonResponse
    {
        $coupon = Coupon::active()->where('slug', $slug)->firstOrFail();

        if (!$coupon->code) {
            return response()->json([
                'success' => false,
                'message' => 'This is a deal without a specific code.',
            ]);
        }

        // Track the code reveal
        AffiliateClick::track($coupon, [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'device_type' => $this->getDeviceType($request->userAgent()),
        ]);

        $coupon->incrementUsage();

        return response()->json([
            'success' => true,
            'code' => $coupon->code,
            'affiliate_url' => $coupon->affiliate_url,
        ]);
    }

    /**
     * Get device type from user agent.
     */
    private function getDeviceType(string $userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        
        return 'desktop';
    }
}
