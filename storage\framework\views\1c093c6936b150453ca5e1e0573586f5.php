<?php $__env->startSection('content'); ?>


<!-- Dynamic Hero Section -->
<section class="hero-swiper">
    <div class="swiper" id="heroSwiper">
        <div class="swiper-wrapper">
            <?php $__currentLoopData = $heroes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hero): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="swiper-slide">
                    <div class="relative text-white overflow-hidden h-[700px] flex items-center"
                        style="background: <?php echo e($hero->background_image ? 'linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4))' : $hero->background_color); ?>;">
                        
                        <?php if($hero->background_image): ?>
                            <div class="absolute inset-0">
                                <img src="<?php echo e($hero->background_image_url); ?>" alt="<?php echo e($hero->title); ?>" class="w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black opacity-40"></div>
                            </div>
                        <?php endif; ?>

                        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                            <div class="text-<?php echo e($hero->text_position); ?> <?php echo e($hero->text_color === 'dark' ? 'text-gray-900' : 'text-white'); ?>">
                                <?php if($hero->subtitle): ?>
                                    <p class="text-lg md:text-xl mb-4 opacity-90 animate-fade-in"><?php echo e($hero->subtitle); ?></p>
                                <?php endif; ?>
                                <h1 class="text-5xl md:text-7xl font-bold mb-6 animate-slide-up">
                                    <?php echo e($hero->title); ?>

                                </h1>
                                <?php if($hero->description): ?>
                                    <p class="text-xl md:text-2xl mb-8 max-w-3xl <?php echo e($hero->text_position === 'center' ? 'mx-auto' : ''); ?> animate-slide-up opacity-90">
                                        <?php echo e($hero->description); ?>

                                    </p>
                                <?php endif; ?>
                                <div class="flex flex-col sm:flex-row gap-4 <?php echo e($hero->text_position === 'center' ? 'justify-center' : ($hero->text_position === 'right' ? 'justify-end' : 'justify-start')); ?> animate-slide-up">
                                    <?php if($hero->primary_button_text && $hero->primary_button_url): ?>
                                        <a href="<?php echo e($hero->primary_button_url); ?>"
                                            class="btn-<?php echo e($hero->primary_button_style); ?> text-lg px-8 py-4">
                                            <?php echo e($hero->primary_button_text); ?>

                                        </a>
                                    <?php endif; ?>
                                    <?php if($hero->secondary_button_text && $hero->secondary_button_url): ?>
                                        <a href="<?php echo e($hero->secondary_button_url); ?>"
                                            class="btn-<?php echo e($hero->secondary_button_style); ?> text-lg px-8 py-4 <?php echo e($hero->text_color === 'dark' ? 'border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white' : 'border-white text-white hover:bg-white hover:text-gray-900'); ?>">
                                            <?php echo e($hero->secondary_button_text); ?>

                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Navigation Controls -->
        <?php if($heroes->count() > 1): ?>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        <?php endif; ?>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-blue-600"><?php echo e(\App\Models\Blog::published()->count()); ?>+</div>
                <div class="text-gray-600">Published Articles</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-green-600"><?php echo e(\App\Models\Category::active()->count()); ?>+</div>
                <div class="text-gray-600">Categories</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-purple-600"><?php echo e(\App\Models\Blog::published()->sum('views_count')); ?>+</div>
                <div class="text-gray-600">Total Views</div>
            </div>
            <div class="space-y-2">
                <div class="text-3xl md:text-4xl font-bold text-orange-600"><?php echo e(\App\Models\Tag::active()->count()); ?>+</div>
                <div class="text-gray-600">Topics Covered</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Articles
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Discover our most popular and insightful articles, handpicked by our editorial team.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            <?php $__empty_1 = true; $__currentLoopData = $featuredBlogs ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" class="block">
                    <article class="card-hover cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-xl">
                            <?php if($blog->featured_image): ?>
                                <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>"
                                     alt="<?php echo e($blog->title); ?>"
                                     class="w-full h-48 object-cover rounded-t-xl">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                    <span class="text-white text-4xl font-bold"><?php echo e(substr($blog->title, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <?php if($blog->primaryCategory): ?>
                                    <span class="badge-primary"><?php echo e($blog->primaryCategory->name); ?></span>
                                <?php endif; ?>
                                <span class="text-sm text-gray-500"><?php echo e($blog->published_at->format('M j, Y')); ?></span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2 line-clamp-2"><?php echo e($blog->title); ?></h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($blog->excerpt); ?></p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <?php if($blog->author): ?>
                                        <span class="text-sm text-gray-500">By <?php echo e($blog->author->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                <span class="text-blue-600 font-medium">Read More →</span>
                            </div>
                        </div>
                    </article>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">✨</div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No Featured Articles Yet</h3>
                    <p class="text-gray-500">Featured articles will appear here once they're published.</p>
                </div>
            <?php endif; ?>
        </div>


    </div>
</section>

<!-- Popular Articles -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Most Popular
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Discover our most-read articles that readers love and share the most.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <?php $__empty_1 = true; $__currentLoopData = $popularBlogs ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" class="block">
                    <article class="card-hover cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-xl">
                            <?php if($blog->featured_image): ?>
                                <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>"
                                     alt="<?php echo e($blog->title); ?>"
                                     class="w-full h-48 object-cover rounded-t-xl">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                    <span class="text-white text-4xl font-bold"><?php echo e(substr($blog->title, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <?php if($blog->primaryCategory): ?>
                                    <span class="badge-primary"><?php echo e($blog->primaryCategory->name); ?></span>
                                <?php endif; ?>
                                <span class="text-sm text-gray-500"><?php echo e($blog->published_at->format('M j, Y')); ?></span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2 line-clamp-2"><?php echo e($blog->title); ?></h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($blog->excerpt); ?></p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <?php if($blog->author): ?>
                                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium"><?php echo e(substr($blog->author->name, 0, 1)); ?></span>
                                        </div>
                                        <span class="text-sm text-gray-500"><?php echo e($blog->author->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                <span class="text-blue-600 font-medium text-sm">Read More →</span>
                            </div>
                        </div>
                    </article>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No Popular Articles Yet</h3>
                    <p class="text-gray-500">Popular articles will appear here once there's more engagement.</p>
                </div>
            <?php endif; ?>
        </div>


    </div>
</section>

<!-- Latest Blog Posts -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Latest Articles
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Stay updated with the latest lifestyle tips, money-saving guides, and product reviews.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            <?php $__empty_1 = true; $__currentLoopData = $latestBlogs ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" class="block">
                    <article class="card-hover cursor-pointer">
                        <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-xl">
                            <?php if($blog->featured_image): ?>
                                <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>"
                                     alt="<?php echo e($blog->title); ?>"
                                     class="w-full h-48 object-cover rounded-t-xl">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 rounded-t-xl flex items-center justify-center">
                                    <span class="text-white text-4xl font-bold"><?php echo e(substr($blog->title, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <?php if($blog->primaryCategory): ?>
                                    <span class="badge-primary"><?php echo e($blog->primaryCategory->name); ?></span>
                                <?php endif; ?>
                                <span class="text-sm text-gray-500"><?php echo e($blog->published_at->format('M j, Y')); ?></span>
                            </div>
                            <h3 class="font-bold text-xl text-gray-900 mb-2 line-clamp-2"><?php echo e($blog->title); ?></h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($blog->excerpt); ?></p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <?php if($blog->author): ?>
                                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-medium"><?php echo e(substr($blog->author->name, 0, 1)); ?></span>
                                        </div>
                                        <span class="text-sm text-gray-500"><?php echo e($blog->author->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                <span class="text-blue-600 font-medium text-sm">Read More →</span>
                            </div>
                        </div>
                    </article>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">📝</div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No Latest Articles Yet</h3>
                    <p class="text-gray-500">Latest articles will appear here once they're published.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center">
            <a href="<?php echo e(route('blog.index')); ?>"
               class="btn-outline text-lg px-8 py-3">
                Read More Articles
            </a>
        </div>
    </div>
</section>

<!-- Advertise With Us - Banner Section -->
<?php if(isset($bannerSettings) && $bannerSettings['enabled'] && isset($banners) && $banners->count() > 0): ?>
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($bannerSettings['title'] ?? 'Advertise With Us'); ?></h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto"><?php echo e($bannerSettings['subtitle'] ?? 'Discover amazing deals and offers from our partners'); ?></p>
        </div>
        <!-- Multiple Banners - Swiper Slider -->
        <div class="banner-swiper">
            <div class="swiper rounded-2xl shadow-lg overflow-hidden" id="bannerSwiper">
                <div class="swiper-wrapper">
                    <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <div class="relative h-64 md:h-80 lg:h-96">
                                <!-- Banner Image -->
                                <img src="<?php echo e($banner->image_url); ?>"
                                     alt="<?php echo e($banner->title ?? 'Banner Image'); ?>"
                                     class="w-full h-full object-cover">

                                <!-- Text Content Overlay (matching admin preview) -->
                                <?php if($banner->title || $banner->description || $banner->link_text): ?>
                                    <div class="absolute inset-0 flex items-center justify-center p-6 md:p-8">
                                        <div class="text-center max-w-2xl <?php echo e($banner->text_color === 'dark' ? 'text-gray-900' : 'text-white'); ?>">
                                            <!-- Title -->
                                            <?php if($banner->title): ?>
                                                <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                                                    <?php echo e($banner->title); ?>

                                                </h3>
                                            <?php endif; ?>

                                            <!-- Description -->
                                            <?php if($banner->description): ?>
                                                <p class="text-lg md:text-xl mb-6 opacity-90">
                                                    <?php echo e($banner->description); ?>

                                                </p>
                                            <?php endif; ?>

                                            <!-- Call-to-Action Button (matching admin preview style) -->
                                            <?php if($banner->link_url && $banner->link_text): ?>
                                                <div class="mt-6">
                                                    <a href="<?php echo e($banner->link_url); ?>"
                                                       class="inline-flex items-center space-x-2 bg-white bg-opacity-20 px-6 py-3 rounded-lg backdrop-blur-sm font-medium transition-all duration-300 hover:bg-opacity-30"
                                                       <?php if($banner->link_target === '_blank'): ?> target="_blank" rel="noopener noreferrer" <?php endif; ?>>
                                                        <span><?php echo e($banner->link_text); ?></span>
                                                        <?php if($banner->link_target === '_blank'): ?>
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-1M14 4h6m0 0v6m0-6L10 14"></path>
                                                            </svg>
                                                        <?php else: ?>
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                            </svg>
                                                        <?php endif; ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Clickable overlay if there's a link but no text content -->
                                <?php if($banner->link_url && !$banner->link_text): ?>
                                    <a href="<?php echo e($banner->link_url); ?>"
                                       class="absolute inset-0 z-10"
                                       <?php if($banner->link_target === '_blank'): ?> target="_blank" rel="noopener noreferrer" <?php endif; ?>>
                                        <span class="sr-only"><?php echo e($banner->title ?? 'View Banner'); ?></span>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Navigation -->
                <?php if($banners->count() > 1): ?>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Tags and Further Reading Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Popular Tags -->
            <div>
                <div class="mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        Popular Tags
                    </h2>
                    <p class="text-gray-600">
                        Explore articles by popular topics and discover content that interests you.
                    </p>
                </div>

                <div class="flex flex-wrap gap-3">
                    <?php $__empty_1 = true; $__currentLoopData = $popularTags ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <a href="<?php echo e(route('blog.index', ['tag' => $tag->slug])); ?>"
                           class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 rounded-full text-sm font-medium transition-colors duration-200 border border-transparent hover:border-blue-200">
                            <span class="w-2 h-2 rounded-full mr-2" style="background-color: <?php echo e($tag->color ?? '#3B82F6'); ?>"></span>
                            <?php echo e($tag->name); ?>

                            <span class="ml-2 text-xs text-gray-500">(<?php echo e($tag->blogs_count); ?>)</span>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8 w-full">
                            <div class="text-gray-400 text-4xl mb-3">🏷️</div>
                            <p class="text-gray-500">No tags available yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Further Reading -->
            <div>
                <div class="mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        Further Reading
                    </h2>
                    <p class="text-gray-600">
                        Discover more interesting articles you might have missed.
                    </p>
                </div>

                <div class="space-y-6">
                    <?php $__empty_1 = true; $__currentLoopData = $furtherReading ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <a href="<?php echo e(route('blog.show', $blog->slug)); ?>" class="block group">
                            <article class="flex space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="flex-shrink-0">
                                    <?php if($blog->featured_image): ?>
                                        <img src="<?php echo e(asset('storage/' . $blog->featured_image)); ?>"
                                             alt="<?php echo e($blog->title); ?>"
                                             class="w-20 h-20 object-cover rounded-lg">
                                    <?php else: ?>
                                        <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                                            <span class="text-white text-lg font-bold"><?php echo e(substr($blog->title, 0, 1)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <?php if($blog->primaryCategory): ?>
                                            <span class="badge-primary text-xs"><?php echo e($blog->primaryCategory->name); ?></span>
                                        <?php endif; ?>
                                        <span class="text-xs text-gray-500"><?php echo e($blog->published_at->format('M j, Y')); ?></span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 mb-1 line-clamp-2 transition-colors duration-200">
                                        <?php echo e($blog->title); ?>

                                    </h3>
                                    <p class="text-sm text-gray-600 line-clamp-2"><?php echo e($blog->excerpt); ?></p>
                                    <div class="flex items-center justify-between mt-2">
                                        <?php if($blog->author): ?>
                                            <span class="text-xs text-gray-500">By <?php echo e($blog->author->name); ?></span>
                                        <?php endif; ?>
                                        <span class="text-xs text-blue-600 group-hover:text-blue-700">Read More →</span>
                                    </div>
                                </div>
                            </article>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8">
                            <div class="text-gray-400 text-4xl mb-3">📖</div>
                            <p class="text-gray-500">No further reading suggestions available.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trusted Partners Section -->
<?php if(isset($partners) && $partners->count() > 0): ?>
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Trusted Partners
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                We collaborate with industry leaders to bring you the best content and experiences.
            </p>
        </div>

        <div class="partner-swiper">
            <div class="swiper" id="partnerSwiper">
                <div class="swiper-wrapper">
                    <?php $__currentLoopData = $partners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <div class="partner-card bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-gray-200 group">
                                <?php if($partner->website_url): ?>
                                    <a href="<?php echo e($partner->website_url); ?>" target="_blank" class="block">
                                <?php endif; ?>
                                        <div class="flex items-center justify-center h-16 mb-4">
                                            <img src="<?php echo e($partner->logo_url); ?>"
                                                 alt="<?php echo e($partner->name); ?>"
                                                 class="max-h-full max-w-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300">
                                        </div>
                                        <h3 class="text-sm font-semibold text-gray-900 text-center mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                            <?php echo e($partner->name); ?>

                                        </h3>
                                        <?php if($partner->category): ?>
                                            <p class="text-xs text-gray-500 text-center">
                                                <?php echo e(ucfirst($partner->category)); ?>

                                            </p>
                                        <?php endif; ?>
                                <?php if($partner->website_url): ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>


        </div>
    </div>
</section>
<?php endif; ?>

<!-- Browse Multi-Niche Blogs by Categories -->
<?php if(isset($browseCategories) && $browseCategories->count() > 0): ?>
<section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Browse Multi-Niche Blogs by Categories
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Explore diverse content across multiple niches and find exactly what interests you.
            </p>
        </div>

        <div class="category-swiper">
            <div class="swiper" id="categorySwiper">
                <div class="swiper-wrapper">
                    <?php $__currentLoopData = $browseCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <a href="<?php echo e(route('blog.index', ['category' => $category->slug])); ?>" class="block group">
                                <div class="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                                    <!-- Background Image -->
                                    <div class="h-64 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                                        <?php if($category->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $category->image)); ?>"
                                                 alt="<?php echo e($category->name); ?>"
                                                 class="w-full h-full object-cover">
                                            <div class="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300"></div>
                                        <?php else: ?>
                                            <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600"></div>
                                        <?php endif; ?>

                                        <!-- Content Overlay -->
                                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-white">
                                            <div class="transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                                                <h3 class="text-xl font-bold mb-2 group-hover:text-yellow-300 transition-colors duration-300">
                                                    <?php echo e($category->name); ?>

                                                </h3>
                                                <p class="text-sm opacity-90 mb-3 line-clamp-2">
                                                    <?php echo e($category->description ?: 'Discover amazing articles in this category'); ?>

                                                </p>
                                                <div class="flex items-center justify-between">
                                                    <span class="text-xs bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                                        <?php echo e($category->blogs_count); ?> <?php echo e(Str::plural('Article', $category->blogs_count)); ?>

                                                    </span>
                                                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center group-hover:bg-yellow-300 group-hover:bg-opacity-100 transition-all duration-300">
                                                        <svg class="w-4 h-4 group-hover:text-gray-900 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Category Icon/Color -->
                                        <div class="absolute top-4 left-4">
                                            <div class="w-3 h-3 rounded-full" style="background-color: <?php echo e($category->color ?? '#3B82F6'); ?>"></div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>


        </div>
    </div>
</section>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Pass banner and hero settings to JavaScript
    window.bannerSettings = <?php echo json_encode($bannerSettings ?? [], 15, 512) ?>;
    window.heroSettings = <?php echo json_encode($heroSettings ?? [], 15, 512) ?>;
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/home.blade.php ENDPATH**/ ?>