Stack trace:
Frame         Function      Args
0007FFFFBE30  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBE30, 0007FFFFAD30) msys-2.0.dll+0x2118E
0007FFFFBE30  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC108) msys-2.0.dll+0x69BA
0007FFFFBE30  0002100469F2 (00021028DF99, 0007FFFFBCE8, 0007FFFFBE30, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBE30  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBE30  00021006A545 (0007FFFFBE40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC110  00021006B9A5 (0007FFFFBE40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE9D9E0000 ntdll.dll
7FFE9C990000 KERNEL32.DLL
7FFE9B180000 KERNELBASE.dll
7FFE9BC20000 USER32.dll
7FFE9AB10000 win32u.dll
7FFE9CAE0000 GDI32.dll
7FFE9AD70000 gdi32full.dll
7FFE9AB40000 msvcp_win.dll
7FFE9B620000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE9D860000 advapi32.dll
7FFE9B770000 msvcrt.dll
7FFE9C0F0000 sechost.dll
7FFE9B910000 RPCRT4.dll
7FFE9A000000 CRYPTBASE.DLL
7FFE9B580000 bcryptPrimitives.dll
7FFE9BDF0000 IMM32.DLL
