<?php $__env->startSection('title', 'Create Banner'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="<?php echo e(route('admin.banners.index')); ?>" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Banner</h1>
            <p class="text-gray-600">Add a new banner to your home page</p>
        </div>
    </div>

    <!-- Form -->
    <form action="<?php echo e(route('admin.banners.store')); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
        <?php echo csrf_field(); ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="form-input <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('title')); ?>" 
                                   required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3" 
                                      class="form-input <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      placeholder="Optional description for the banner"><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="image" class="form-label">Banner Image *</label>
                            <input type="file" 
                                   id="image" 
                                   name="image" 
                                   accept="image/*" 
                                   class="form-input <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   required>
                            <p class="text-sm text-gray-500 mt-1">Recommended size: 1920x600px. Max size: 2MB</p>
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Link Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Link Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="link_url" class="form-label">Link URL</label>
                            <input type="url" 
                                   id="link_url" 
                                   name="link_url" 
                                   class="form-input <?php $__errorArgs = ['link_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('link_url')); ?>" 
                                   placeholder="https://example.com">
                            <?php $__errorArgs = ['link_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="link_text" class="form-label">Button Text *</label>
                            <input type="text" 
                                   id="link_text" 
                                   name="link_text" 
                                   class="form-input <?php $__errorArgs = ['link_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('link_text', 'Learn More')); ?>" 
                                   required>
                            <?php $__errorArgs = ['link_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="button_style" class="form-label">Button Style *</label>
                            <select id="button_style" 
                                    name="button_style" 
                                    class="form-input <?php $__errorArgs = ['button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    required>
                                <option value="primary" <?php echo e(old('button_style') === 'primary' ? 'selected' : ''); ?>>Primary (Gradient)</option>
                                <option value="secondary" <?php echo e(old('button_style') === 'secondary' ? 'selected' : ''); ?>>Secondary (Solid)</option>
                                <option value="outline" <?php echo e(old('button_style') === 'outline' ? 'selected' : ''); ?>>Outline</option>
                            </select>
                            <?php $__errorArgs = ['button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="text_position" class="form-label">Text Position *</label>
                            <select id="text_position" 
                                    name="text_position" 
                                    class="form-input <?php $__errorArgs = ['text_position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    required>
                                <option value="left" <?php echo e(old('text_position') === 'left' ? 'selected' : ''); ?>>Left</option>
                                <option value="center" <?php echo e(old('text_position', 'center') === 'center' ? 'selected' : ''); ?>>Center</option>
                                <option value="right" <?php echo e(old('text_position') === 'right' ? 'selected' : ''); ?>>Right</option>
                            </select>
                            <?php $__errorArgs = ['text_position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="text_color" class="form-label">Text Color *</label>
                            <select id="text_color" 
                                    name="text_color" 
                                    class="form-input <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    required>
                                <option value="white" <?php echo e(old('text_color', 'white') === 'white' ? 'selected' : ''); ?>>White</option>
                                <option value="dark" <?php echo e(old('text_color') === 'dark' ? 'selected' : ''); ?>>Dark</option>
                            </select>
                            <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('sort_order', 0)); ?>" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Schedule Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Schedule Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="starts_at" class="form-label">Start Date</label>
                            <input type="datetime-local" 
                                   id="starts_at" 
                                   name="starts_at" 
                                   class="form-input <?php $__errorArgs = ['starts_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('starts_at')); ?>">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to start immediately</p>
                            <?php $__errorArgs = ['starts_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="expires_at" class="form-label">End Date</label>
                            <input type="datetime-local" 
                                   id="expires_at" 
                                   name="expires_at" 
                                   class="form-input <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('expires_at')); ?>">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to never expire</p>
                            <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Preview -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Preview</h3>
                    </div>
                    <div class="card-body">
                        <div id="banner-preview" class="rounded-lg overflow-hidden border border-gray-200 bg-gray-50">
                            <!-- Banner Image Preview -->
                            <div class="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                <img id="preview-image" class="w-full h-full object-cover hidden" alt="Banner preview">
                                <div id="preview-placeholder" class="text-white text-center">
                                    <svg class="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="text-sm opacity-75">Banner Image Preview</p>
                                </div>

                                <!-- Text Overlay -->
                                <div id="preview-overlay" class="absolute inset-0 flex items-center justify-center p-6">
                                    <div id="preview-content" class="text-center text-white">
                                        <h3 id="preview-title" class="text-2xl font-bold mb-2">Banner Title</h3>
                                        <p id="preview-description" class="text-lg mb-4 opacity-90" style="display: none;">Banner Description</p>
                                        <div id="preview-link" class="inline-flex items-center space-x-2 bg-white bg-opacity-20 px-4 py-2 rounded-lg backdrop-blur-sm" style="display: none;">
                                            <span id="preview-link-text" class="font-medium">Learn More</span>
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-1M14 4h6m0 0v6m0-6L10 14"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Info -->
                            <div class="p-4 bg-white">
                                <div class="flex items-center justify-between text-sm text-gray-600">
                                    <div class="flex items-center space-x-4">
                                        <span id="preview-status" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Active</span>
                                        <span id="preview-order">Order: <span id="preview-order-value">0</span></span>
                                    </div>
                                    <div id="preview-link-info" class="text-xs text-gray-500" style="display: none;">
                                        <span id="preview-link-target">Same Tab</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 text-sm text-gray-600">
                            <p class="font-medium mb-2">Preview Notes:</p>
                            <ul class="text-xs space-y-1 text-gray-500">
                                <li>• Upload an image to see the actual banner preview</li>
                                <li>• Text overlay will adapt to the selected text color</li>
                                <li>• Link button appears when link text is provided</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="<?php echo e(route('admin.banners.index')); ?>" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Create Banner</button>
        </div>
    </form>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get form inputs
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const imageInput = document.getElementById('image');
    const linkTextInput = document.getElementById('link_text');
    const linkUrlInput = document.getElementById('link_url');
    const linkTargetInput = document.getElementById('link_target');
    const textColorInput = document.getElementById('text_color');
    const sortOrderInput = document.getElementById('sort_order');
    const isActiveInput = document.getElementById('is_active');

    // Get preview elements
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const previewImage = document.getElementById('preview-image');
    const previewPlaceholder = document.getElementById('preview-placeholder');
    const previewOverlay = document.getElementById('preview-overlay');
    const previewContent = document.getElementById('preview-content');
    const previewLink = document.getElementById('preview-link');
    const previewLinkText = document.getElementById('preview-link-text');
    const previewStatus = document.getElementById('preview-status');
    const previewOrderValue = document.getElementById('preview-order-value');
    const previewLinkInfo = document.getElementById('preview-link-info');
    const previewLinkTarget = document.getElementById('preview-link-target');

    // Update preview function
    function updatePreview() {
        // Update title
        previewTitle.textContent = titleInput.value || 'Banner Title';

        // Update description
        if (descriptionInput.value) {
            previewDescription.textContent = descriptionInput.value;
            previewDescription.style.display = 'block';
        } else {
            previewDescription.style.display = 'none';
        }

        // Update link
        if (linkTextInput.value) {
            previewLinkText.textContent = linkTextInput.value;
            previewLink.style.display = 'inline-flex';
            previewLinkInfo.style.display = 'block';
            previewLinkTarget.textContent = linkTargetInput.value === '_blank' ? 'New Tab' : 'Same Tab';
        } else {
            previewLink.style.display = 'none';
            previewLinkInfo.style.display = 'none';
        }

        // Update text color
        const textColor = textColorInput.value === 'dark' ? '#1F2937' : '#FFFFFF';
        previewContent.style.color = textColor;

        // Update status
        if (isActiveInput.checked) {
            previewStatus.textContent = 'Active';
            previewStatus.className = 'px-2 py-1 bg-green-100 text-green-800 rounded text-xs';
        } else {
            previewStatus.textContent = 'Inactive';
            previewStatus.className = 'px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs';
        }

        // Update sort order
        previewOrderValue.textContent = sortOrderInput.value || '0';
    }

    // Handle image upload preview
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                previewImage.classList.remove('hidden');
                previewPlaceholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            previewImage.classList.add('hidden');
            previewPlaceholder.style.display = 'block';
        }
    });

    // Add event listeners for live preview
    [titleInput, descriptionInput, linkTextInput, linkUrlInput, linkTargetInput, textColorInput, sortOrderInput, isActiveInput].forEach(input => {
        if (input) {
            input.addEventListener('input', updatePreview);
            input.addEventListener('change', updatePreview);
        }
    });

    // Initial preview update
    updatePreview();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/admin/banners/create.blade.php ENDPATH**/ ?>