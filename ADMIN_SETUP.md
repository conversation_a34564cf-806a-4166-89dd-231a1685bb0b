# Admin User Setup Instructions

## Create Admin User

To create an admin user for the blog application, run the following commands:

### Option 1: Using Seeder (Recommended)
```bash
php artisan db:seed --class=AdminUserSeeder
```

This will create:
- **Admin User**: <EMAIL> / password123
- **Demo Users**: <EMAIL> / password123, <EMAIL> / password123

### Option 2: Using Tinker
```bash
php artisan tinker
```

Then run:
```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'email_verified_at' => now(),
]);
```

### Option 3: Manual Database Insert
```sql
INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at) 
VALUES (
    'Admin User', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    NOW(), 
    NOW(), 
    NOW()
);
```
*Note: The password hash above is for 'password'*

## Login Credentials

After creating the admin user, you can login at:
- **URL**: `/login`
- **Email**: <EMAIL>
- **Password**: password123

## Admin Panel Access

Once logged in, you can access the admin panel at:
- **URL**: `/admin`

## Features Available

### Blog Management
- Create, edit, delete blog posts
- Manage categories and tags
- Set featured posts
- Upload images

### Banner Management
- Create dynamic home page banners
- Upload banner images
- Set scheduling and positioning
- Control text styling

### Content Organization
- Manage blog categories
- Create and organize tags
- Control navigation structure

## Database Migrations

Make sure to run all migrations:
```bash
php artisan migrate
```

This will create tables for:
- users
- blogs
- categories
- tags
- banners
- blog_category (pivot)
- blog_tag (pivot)

## Storage Setup

Create storage link for file uploads:
```bash
php artisan storage:link
```

This enables image uploads for blogs and banners.
