@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                🎫 Best Coupons & Deals
            </h1>
            <p class="text-xl text-green-100 max-w-2xl mx-auto">
                Save money with verified coupon codes and exclusive deals from top brands. Updated daily!
            </p>
        </div>
    </div>
</section>

<!-- Filters & Search -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
                <form action="{{ route('coupons.index') }}" method="GET" class="relative">
                    <input type="text" 
                           name="search" 
                           placeholder="Search coupons and deals..." 
                           value="{{ request('search') }}"
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    @foreach(['store', 'category', 'type', 'sort'] as $param)
                        @if(request($param))
                            <input type="hidden" name="{{ $param }}" value="{{ request($param) }}">
                        @endif
                    @endforeach
                </form>
            </div>

            <!-- Store Filter -->
            <div>
                <select name="store" 
                        onchange="updateFilters()"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <option value="">All Stores</option>
                    @foreach($stores ?? [] as $slug => $name)
                        <option value="{{ $slug }}" {{ request('store') === $slug ? 'selected' : '' }}>
                            {{ $name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Category Filter -->
            <div>
                <select name="category" 
                        onchange="updateFilters()"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <option value="">All Categories</option>
                    @foreach($categories ?? [] as $slug => $name)
                        <option value="{{ $slug }}" {{ request('category') === $slug ? 'selected' : '' }}>
                            {{ $name }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>

        <!-- Additional Filters -->
        <div class="flex flex-wrap gap-4 mt-4">
            <select name="type" 
                    onchange="updateFilters()"
                    class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                <option value="">All Types</option>
                <option value="coupon" {{ request('type') === 'coupon' ? 'selected' : '' }}>Coupons</option>
                <option value="deal" {{ request('type') === 'deal' ? 'selected' : '' }}>Deals</option>
                <option value="cashback" {{ request('type') === 'cashback' ? 'selected' : '' }}>Cashback</option>
                <option value="freebie" {{ request('type') === 'freebie' ? 'selected' : '' }}>Freebies</option>
            </select>

            <select name="sort" 
                    onchange="updateFilters()"
                    class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest First</option>
                <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                <option value="expiring" {{ request('sort') === 'expiring' ? 'selected' : '' }}>Expiring Soon</option>
            </select>
        </div>
    </div>
</section>

<!-- Featured Coupons -->
@if(isset($featuredCoupons) && $featuredCoupons->count() > 0)
<section class="py-12 bg-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">⭐ Featured Deals</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($featuredCoupons as $coupon)
                @include('components.coupon-card', ['coupon' => $coupon])
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Expiring Soon -->
@if(isset($expiringSoon) && $expiringSoon->count() > 0)
<section class="py-12 bg-red-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">⏰ Expiring Soon</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($expiringSoon as $coupon)
                @include('components.coupon-card', ['coupon' => $coupon])
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Main Coupons Grid -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(request()->hasAny(['search', 'store', 'category', 'type']))
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">
                    @if(request('search'))
                        Search Results for "{{ request('search') }}"
                    @else
                        Filtered Results
                    @endif
                </h2>
                <p class="text-gray-600 mt-1">{{ $coupons->total() ?? 0 }} {{ Str::plural('coupon', $coupons->total() ?? 0) }} found</p>
            </div>
        @else
            <h2 class="text-2xl font-bold text-gray-900 mb-8">🎯 All Coupons & Deals</h2>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($coupons ?? [] as $coupon)
                @include('components.coupon-card', ['coupon' => $coupon])
            @empty
                <!-- Demo Content -->
                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <span class="text-red-600 font-bold text-lg">A</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Amazon</h3>
                                    <p class="text-sm text-gray-500">Electronics</p>
                                </div>
                            </div>
                            <span class="badge-danger">50% OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Up to 50% Off Electronics</h4>
                        <p class="text-gray-600 text-sm mb-4">Save big on laptops, smartphones, and gadgets. Limited time offer!</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Expires: Dec 31, 2024</span>
                            <button class="btn-primary text-sm">Get Deal</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-bold text-lg">N</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Nike</h3>
                                    <p class="text-sm text-gray-500">Sports</p>
                                </div>
                            </div>
                            <span class="badge-success">30% OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">30% Off Athletic Wear</h4>
                        <p class="text-gray-600 text-sm mb-4">Get the latest Nike shoes and apparel at unbeatable prices.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Code: NIKE30</span>
                            <button class="btn-primary text-sm">Copy Code</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <span class="text-green-600 font-bold text-lg">S</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Starbucks</h3>
                                    <p class="text-sm text-gray-500">Food</p>
                                </div>
                            </div>
                            <span class="badge-warning">BOGO</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Buy One Get One Free</h4>
                        <p class="text-gray-600 text-sm mb-4">Enjoy your favorite drinks with a friend. Valid on all beverages.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Valid Today</span>
                            <button class="btn-primary text-sm">Get Offer</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <span class="text-purple-600 font-bold text-lg">U</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Uber Eats</h3>
                                    <p class="text-sm text-gray-500">Food Delivery</p>
                                </div>
                            </div>
                            <span class="badge-success">FREE</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Free Delivery on First Order</h4>
                        <p class="text-gray-600 text-sm mb-4">Get your favorite meals delivered for free. New users only.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Code: WELCOME</span>
                            <button class="btn-primary text-sm">Copy Code</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <span class="text-yellow-600 font-bold text-lg">M</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">McDonald's</h3>
                                    <p class="text-sm text-gray-500">Fast Food</p>
                                </div>
                            </div>
                            <span class="badge-danger">$5 OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">$5 Off Orders Over $15</h4>
                        <p class="text-gray-600 text-sm mb-4">Save on your next McDonald's order. Valid for mobile orders only.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">App Only</span>
                            <button class="btn-primary text-sm">Get Deal</button>
                        </div>
                    </div>
                </div>

                <div class="card-hover">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <span class="text-indigo-600 font-bold text-lg">B</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Best Buy</h3>
                                    <p class="text-sm text-gray-500">Electronics</p>
                                </div>
                            </div>
                            <span class="badge-warning">20% OFF</span>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">20% Off Gaming Accessories</h4>
                        <p class="text-gray-600 text-sm mb-4">Upgrade your gaming setup with discounted controllers, headsets, and more.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Expires: Jan 15, 2025</span>
                            <button class="btn-primary text-sm">Get Deal</button>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if(isset($coupons) && $coupons->hasPages())
            <div class="mt-12">
                {{ $coupons->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</section>

<script>
function updateFilters() {
    const params = new URLSearchParams(window.location.search);
    
    // Get current filter values
    const store = document.querySelector('select[name="store"]').value;
    const category = document.querySelector('select[name="category"]').value;
    const type = document.querySelector('select[name="type"]').value;
    const sort = document.querySelector('select[name="sort"]').value;
    
    // Update URL parameters
    if (store) params.set('store', store);
    else params.delete('store');
    
    if (category) params.set('category', category);
    else params.delete('category');
    
    if (type) params.set('type', type);
    else params.delete('type');
    
    if (sort) params.set('sort', sort);
    else params.delete('sort');
    
    // Redirect with new parameters
    window.location.href = '{{ route("coupons.index") }}?' + params.toString();
}
</script>
@endsection
