@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <!-- Header -->
    <section class="relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
            <div class="animate-pulse mb-4">
                <span class="inline-flex items-center px-4 py-2 bg-yellow-400 text-black rounded-full text-sm font-bold">
                    🔥 EXCLUSIVE PROMOTIONS
                </span>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Deals & Offers
            </h1>
            <p class="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
                Discover amazing deals, exclusive coupons, and special offers from top brands and stores.
            </p>
        </div>
    </section>

    <!-- Main Tab Navigation -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center mb-8">
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-2 border border-white/20">
                    <div class="flex space-x-2">
                        <button onclick="switchMainTab('coupons')" 
                                id="coupons-main-tab" 
                                class="main-tab-button active px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                            💳 Coupons
                        </button>
                        <button onclick="switchMainTab('stores')" 
                                id="stores-main-tab" 
                                class="main-tab-button px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                            🏪 Stores
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Coupons Content -->
    <div id="coupons-content" class="main-tab-content">
        <!-- Coupons Sub-tabs -->
        <section class="pb-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-center mb-8">
                    <div class="bg-white/5 backdrop-blur-sm rounded-lg p-1 border border-white/10">
                        <div class="flex space-x-1">
                            <button onclick="switchSubTab('coupons', 'featured')" 
                                    id="featured-coupons-tab" 
                                    class="sub-tab-button active px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                ⭐ Featured
                            </button>
                            <button onclick="switchSubTab('coupons', 'popular')" 
                                    id="popular-coupons-tab" 
                                    class="sub-tab-button px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                🔥 Popular
                            </button>
                            <button onclick="switchSubTab('coupons', 'latest')" 
                                    id="latest-coupons-tab" 
                                    class="sub-tab-button px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                🆕 Latest
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Coupons -->
        <div id="featured-coupons" class="sub-tab-content">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">⭐ Featured Coupons</h2>
                        <p class="text-xl text-gray-300">Hand-picked exclusive deals from top brands</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($featuredCoupons ?? [] as $coupon)
                            @include('components.coupon-card-promo', ['coupon' => $coupon])
                        @empty
                            @include('components.demo-coupons', ['type' => 'featured'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>

        <!-- Popular Coupons -->
        <div id="popular-coupons" class="sub-tab-content hidden">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">🔥 Popular Coupons</h2>
                        <p class="text-xl text-gray-300">Most used deals by our community</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($popularCoupons ?? [] as $coupon)
                            @include('components.coupon-card-promo', ['coupon' => $coupon])
                        @empty
                            @include('components.demo-coupons', ['type' => 'popular'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>

        <!-- Latest Coupons -->
        <div id="latest-coupons" class="sub-tab-content hidden">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">🆕 Latest Coupons</h2>
                        <p class="text-xl text-gray-300">Fresh deals added recently</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($latestCoupons ?? [] as $coupon)
                            @include('components.coupon-card-promo', ['coupon' => $coupon])
                        @empty
                            @include('components.demo-coupons', ['type' => 'latest'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Stores Content -->
    <div id="stores-content" class="main-tab-content hidden">
        <!-- Stores Sub-tabs -->
        <section class="pb-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-center mb-8">
                    <div class="bg-white/5 backdrop-blur-sm rounded-lg p-1 border border-white/10">
                        <div class="flex space-x-1">
                            <button onclick="switchSubTab('stores', 'featured')" 
                                    id="featured-stores-tab" 
                                    class="sub-tab-button active px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                ⭐ Featured
                            </button>
                            <button onclick="switchSubTab('stores', 'popular')" 
                                    id="popular-stores-tab" 
                                    class="sub-tab-button px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                🔥 Popular
                            </button>
                            <button onclick="switchSubTab('stores', 'latest')" 
                                    id="latest-stores-tab" 
                                    class="sub-tab-button px-4 py-2 rounded-md text-sm font-medium transition-all duration-300">
                                🆕 Latest
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Stores -->
        <div id="featured-stores" class="sub-tab-content">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">⭐ Featured Stores</h2>
                        <p class="text-xl text-gray-300">Top-rated stores with exclusive partnerships</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @forelse($featuredStores ?? [] as $store)
                            @include('components.store-card-promo', ['store' => $store])
                        @empty
                            @include('components.demo-stores', ['type' => 'featured'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>

        <!-- Popular Stores -->
        <div id="popular-stores" class="sub-tab-content hidden">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">🔥 Popular Stores</h2>
                        <p class="text-xl text-gray-300">Most visited stores by our users</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @forelse($popularStores ?? [] as $store)
                            @include('components.store-card-promo', ['store' => $store])
                        @empty
                            @include('components.demo-stores', ['type' => 'popular'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>

        <!-- Latest Stores -->
        <div id="latest-stores" class="sub-tab-content hidden">
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-white mb-4">🆕 Latest Stores</h2>
                        <p class="text-xl text-gray-300">Newly added stores and partnerships</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @forelse($latestStores ?? [] as $store)
                            @include('components.store-card-promo', ['store' => $store])
                        @empty
                            @include('components.demo-stores', ['type' => 'latest'])
                        @endforelse
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>

<style>
.main-tab-button.active {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
}

.main-tab-button {
    color: #d1d5db;
}

.sub-tab-button.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.sub-tab-button {
    color: #9ca3af;
}
</style>

<script>
function switchMainTab(tab) {
    // Hide all main tab contents
    document.querySelectorAll('.main-tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all main tab buttons
    document.querySelectorAll('.main-tab-button').forEach(button => {
        button.classList.remove('active');
        button.style.color = '#d1d5db';
    });

    // Show selected tab content
    document.getElementById(tab + '-content').classList.remove('hidden');

    // Add active class to selected tab button
    const activeButton = document.getElementById(tab + '-main-tab');
    activeButton.classList.add('active');
    activeButton.style.color = 'white';
}

function switchSubTab(mainTab, subTab) {
    // Hide all sub tab contents for this main tab
    document.querySelectorAll(`#${mainTab}-content .sub-tab-content`).forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all sub tab buttons for this main tab
    document.querySelectorAll(`#${mainTab}-content .sub-tab-button`).forEach(button => {
        button.classList.remove('active');
    });

    // Show selected sub tab content
    document.getElementById(subTab + '-' + mainTab).classList.remove('hidden');

    // Add active class to selected sub tab button
    document.getElementById(subTab + '-' + mainTab + '-tab').classList.add('active');
}

function copyCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        // Show success message
        alert(`Code "${code}" copied to clipboard!`);
    });
}
</script>
@endsection
