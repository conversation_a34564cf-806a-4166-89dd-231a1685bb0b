<?php

namespace Database\Seeders;

use App\Models\Partner;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $partners = [
            [
                'name' => 'TechCorp Solutions',
                'description' => 'Leading technology solutions provider specializing in AI and cloud computing',
                'logo' => 'partners/techcorp-logo.png',
                'website_url' => 'https://techcorp.example.com',
                'category' => 'technology',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'MediaHub Digital',
                'description' => 'Digital media and content platform for modern creators',
                'logo' => 'partners/mediahub-logo.png',
                'website_url' => 'https://mediahub.example.com',
                'category' => 'media',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'RetailMax Pro',
                'description' => 'E-commerce and retail solutions for growing businesses',
                'logo' => 'partners/retailmax-logo.png',
                'website_url' => 'https://retailmax.example.com',
                'category' => 'retail',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'HealthPlus Wellness',
                'description' => 'Healthcare and wellness platform for better living',
                'logo' => 'partners/healthplus-logo.png',
                'website_url' => 'https://healthplus.example.com',
                'category' => 'healthcare',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'EduLearn Academy',
                'description' => 'Online education and learning platform for all ages',
                'logo' => 'partners/edulearn-logo.png',
                'website_url' => 'https://edulearn.example.com',
                'category' => 'education',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'FinanceWise Consulting',
                'description' => 'Financial services and consulting for smart investments',
                'logo' => 'partners/financewise-logo.png',
                'website_url' => 'https://financewise.example.com',
                'category' => 'finance',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'TravelGo Adventures',
                'description' => 'Travel and tourism services for unforgettable experiences',
                'logo' => 'partners/travelgo-logo.png',
                'website_url' => 'https://travelgo.example.com',
                'category' => 'travel',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'FoodieHub Kitchen',
                'description' => 'Food and restaurant platform for culinary enthusiasts',
                'logo' => 'partners/foodiehub-logo.png',
                'website_url' => 'https://foodiehub.example.com',
                'category' => 'food',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'GreenEarth Eco',
                'description' => 'Sustainable living and environmental solutions',
                'logo' => 'partners/greenearth-logo.png',
                'website_url' => 'https://greenearth.example.com',
                'category' => 'environment',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'name' => 'FitLife Gym',
                'description' => 'Fitness and wellness center for healthy lifestyle',
                'logo' => 'partners/fitlife-logo.png',
                'website_url' => 'https://fitlife.example.com',
                'category' => 'fitness',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'name' => 'CreativeStudio Design',
                'description' => 'Design and creative services for brands and businesses',
                'logo' => 'partners/creativestudio-logo.png',
                'website_url' => 'https://creativestudio.example.com',
                'category' => 'design',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 11,
            ],
            [
                'name' => 'SmartHome Tech',
                'description' => 'Smart home automation and IoT solutions',
                'logo' => 'partners/smarthome-logo.png',
                'website_url' => 'https://smarthome.example.com',
                'category' => 'technology',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 12,
            ],
        ];

        foreach ($partners as $partner) {
            Partner::create($partner);
        }
    }
}
