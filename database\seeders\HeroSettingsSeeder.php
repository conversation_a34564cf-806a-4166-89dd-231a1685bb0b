<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class HeroSettingsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Hero section settings
        Setting::set(
            'hero_section_enabled',
            true,
            'boolean',
            'heroes',
            'Enable or disable the entire hero section on homepage'
        );

        Setting::set(
            'hero_auto_hide_when_empty',
            true,
            'boolean',
            'heroes',
            'Automatically hide hero section when no active heroes are available'
        );

        Setting::set(
            'hero_max_display',
            1,
            'integer',
            'heroes',
            'Maximum number of heroes to display on homepage (usually 1 for hero sections)'
        );

        Setting::set(
            'hero_transition_speed',
            5000,
            'integer',
            'heroes',
            'Auto-slide duration in milliseconds - how long each hero is displayed before transitioning to the next (only applies when multiple heroes are active)'
        );
    }
}
