@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Search Header -->
    <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">
                    Search Results for "{{ $query }}"
                </h1>
                <p class="text-lg text-gray-600">
                    Found {{ $totalResults }} {{ Str::plural('result', $totalResults) }} across all content
                </p>
            </div>

            <!-- Search Form -->
            <div class="max-w-2xl mx-auto">
                <form action="{{ route('search') }}" method="GET" class="relative">
                    <input type="text" 
                           name="q" 
                           value="{{ $query }}"
                           placeholder="Search for deals, stores, articles..." 
                           class="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <button type="submit" class="absolute right-2 top-2 bottom-2 px-6 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Search
                    </button>
                </form>
            </div>

            <!-- Filter Tabs -->
            <div class="flex justify-center mt-8">
                <nav class="flex space-x-8">
                    <a href="{{ route('search', ['q' => $query, 'type' => 'all']) }}" 
                       class="px-3 py-2 text-sm font-medium {{ $type === 'all' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                        All ({{ $totalResults }})
                    </a>
                    @if(isset($results['coupons']) && $results['coupons']->count() > 0)
                        <a href="{{ route('search', ['q' => $query, 'type' => 'coupons']) }}" 
                           class="px-3 py-2 text-sm font-medium {{ $type === 'coupons' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                            Coupons ({{ $results['coupons']->count() }})
                        </a>
                    @endif
                    @if(isset($results['stores']) && $results['stores']->count() > 0)
                        <a href="{{ route('search', ['q' => $query, 'type' => 'stores']) }}" 
                           class="px-3 py-2 text-sm font-medium {{ $type === 'stores' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                            Stores ({{ $results['stores']->count() }})
                        </a>
                    @endif
                    @if(isset($results['blogs']) && $results['blogs']->count() > 0)
                        <a href="{{ route('search', ['q' => $query, 'type' => 'blogs']) }}" 
                           class="px-3 py-2 text-sm font-medium {{ $type === 'blogs' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">
                            Articles ({{ $results['blogs']->count() }})
                        </a>
                    @endif
                </nav>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($totalResults > 0)
                <!-- Coupons Results -->
                @if(($type === 'all' || $type === 'coupons') && isset($results['coupons']) && $results['coupons']->count() > 0)
                    <div class="mb-12">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">🎫 Coupons & Deals</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($results['coupons'] as $coupon)
                                @include('components.coupon-card', ['coupon' => $coupon])
                            @endforeach
                        </div>
                        @if($type === 'all' && $results['coupons']->count() >= 8)
                            <div class="text-center mt-6">
                                <a href="{{ route('search', ['q' => $query, 'type' => 'coupons']) }}" 
                                   class="btn-outline">
                                    View All Coupon Results
                                </a>
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Stores Results -->
                @if(($type === 'all' || $type === 'stores') && isset($results['stores']) && $results['stores']->count() > 0)
                    <div class="mb-12">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">🏪 Stores</h2>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                            @foreach($results['stores'] as $store)
                                @include('components.store-card', ['store' => $store])
                            @endforeach
                        </div>
                        @if($type === 'all' && $results['stores']->count() >= 6)
                            <div class="text-center mt-6">
                                <a href="{{ route('search', ['q' => $query, 'type' => 'stores']) }}" 
                                   class="btn-outline">
                                    View All Store Results
                                </a>
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Blog Results -->
                @if(($type === 'all' || $type === 'blogs') && isset($results['blogs']) && $results['blogs']->count() > 0)
                    <div class="mb-12">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">📚 Articles</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($results['blogs'] as $blog)
                                @include('components.blog-card', ['blog' => $blog])
                            @endforeach
                        </div>
                        @if($type === 'all' && $results['blogs']->count() >= 5)
                            <div class="text-center mt-6">
                                <a href="{{ route('search', ['q' => $query, 'type' => 'blogs']) }}" 
                                   class="btn-outline">
                                    View All Article Results
                                </a>
                            </div>
                        @endif
                    </div>
                @endif
            @else
                <!-- No Results -->
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <svg class="w-24 h-24 text-gray-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">No results found</h3>
                        <p class="text-gray-600 mb-8">
                            We couldn't find anything matching "{{ $query }}". Try adjusting your search terms or browse our categories.
                        </p>
                        
                        <!-- Search Suggestions -->
                        <div class="space-y-4">
                            <h4 class="font-semibold text-gray-900">Try searching for:</h4>
                            <div class="flex flex-wrap justify-center gap-2">
                                <a href="{{ route('search', ['q' => 'fashion']) }}" class="badge-primary hover:bg-blue-200">Fashion</a>
                                <a href="{{ route('search', ['q' => 'electronics']) }}" class="badge-primary hover:bg-blue-200">Electronics</a>
                                <a href="{{ route('search', ['q' => 'food']) }}" class="badge-primary hover:bg-blue-200">Food</a>
                                <a href="{{ route('search', ['q' => 'travel']) }}" class="badge-primary hover:bg-blue-200">Travel</a>
                                <a href="{{ route('search', ['q' => 'home']) }}" class="badge-primary hover:bg-blue-200">Home</a>
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div class="mt-8 space-y-2">
                            <a href="{{ route('coupons.index') }}" class="btn-primary">Browse All Coupons</a>
                            <a href="{{ route('stores.index') }}" class="btn-outline">View All Stores</a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Popular Searches -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Popular Searches</h2>
                <p class="text-gray-600">Discover what others are searching for</p>
            </div>
            
            <div class="flex flex-wrap justify-center gap-3">
                @php
                    $popularSearches = [
                        'Amazon coupons', 'Nike deals', 'Black Friday', 'Free shipping',
                        'Electronics sale', 'Fashion discounts', 'Food delivery', 'Travel deals',
                        'Home decor', 'Beauty products', 'Fitness gear', 'Books discount'
                    ];
                @endphp
                
                @foreach($popularSearches as $search)
                    <a href="{{ route('search', ['q' => $search]) }}" 
                       class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors text-sm">
                        {{ $search }}
                    </a>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Search Tips -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Search Tips</h2>
                <p class="text-gray-600">Get better results with these search tips</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="card">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-gray-900">Use Specific Terms</h3>
                        </div>
                        <p class="text-gray-600 text-sm">
                            Search for specific brands, products, or categories like "Nike shoes" or "iPhone deals" for better results.
                        </p>
                    </div>
                </div>
                
                <div class="card">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-gray-900">Try Different Keywords</h3>
                        </div>
                        <p class="text-gray-600 text-sm">
                            If you don't find what you're looking for, try synonyms or related terms like "discount" instead of "coupon".
                        </p>
                    </div>
                </div>
                
                <div class="card">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-gray-900">Use Filters</h3>
                        </div>
                        <p class="text-gray-600 text-sm">
                            Use the filter tabs above to narrow down results to coupons, stores, or articles only.
                        </p>
                    </div>
                </div>
                
                <div class="card">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-gray-900">Browse Categories</h3>
                        </div>
                        <p class="text-gray-600 text-sm">
                            Can't find what you need? Browse our organized categories to discover deals by topic.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
