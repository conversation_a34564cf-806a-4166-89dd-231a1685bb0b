<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Blog;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class CategoryController extends Controller
{
    /**
     * Show all categories.
     */
    public function index(): View
    {
        $data = Cache::remember('categories_index', 3600, function () {
            $parentCategories = Category::active()
                                      ->parent()
                                      ->with(['children' => function ($query) {
                                          $query->active()->ordered();
                                      }])
                                      ->withCount(['blogs', 'coupons'])
                                      ->ordered()
                                      ->get();

            $featuredCategories = Category::active()
                                        ->whereHas('blogs', function ($query) {
                                            $query->published();
                                        })
                                        ->orWhereHas('coupons', function ($query) {
                                            $query->active();
                                        })
                                        ->withCount(['blogs', 'coupons'])
                                        ->ordered()
                                        ->limit(12)
                                        ->get();

            return compact('parentCategories', 'featuredCategories');
        });

        $seoData = [
            'title' => 'Categories - Browse by Topic',
            'description' => 'Browse our content by categories. Find deals, coupons, and articles organized by your interests.',
            'canonical' => url('/categories'),
        ];

        return view('categories.index', array_merge($data, compact('seoData')));
    }

    /**
     * Show a specific category with its content.
     */
    public function show(string $slug, Request $request): View
    {
        $category = Category::active()->where('slug', $slug)->firstOrFail();
        
        $type = $request->get('type', 'all'); // all, blogs, coupons
        $page = $request->get('page', 1);
        
        $cacheKey = "category_{$slug}_{$type}_{$page}";

        $data = Cache::remember($cacheKey, 3600, function () use ($category, $type) {
            $blogs = collect();
            $coupons = collect();

            if ($type === 'all' || $type === 'blogs') {
                $blogs = Blog::published()
                           ->byCategory($category->slug)
                           ->with(['primaryCategory', 'author'])
                           ->latest('published_at')
                           ->paginate(12, ['*'], 'blogs_page');
            }

            if ($type === 'all' || $type === 'coupons') {
                $coupons = Coupon::active()
                                ->where('category_id', $category->id)
                                ->with(['store'])
                                ->ordered()
                                ->paginate(12, ['*'], 'coupons_page');
            }

            // Get subcategories
            $subcategories = $category->children()
                                   ->active()
                                   ->withCount(['blogs', 'coupons'])
                                   ->ordered()
                                   ->get();

            // Get related categories
            $relatedCategories = Category::active()
                                       ->where('id', '!=', $category->id)
                                       ->where('parent_id', $category->parent_id)
                                       ->withCount(['blogs', 'coupons'])
                                       ->ordered()
                                       ->limit(6)
                                       ->get();

            return compact('blogs', 'coupons', 'subcategories', 'relatedCategories');
        });

        $seoData = [
            'title' => $category->meta_title,
            'description' => $category->meta_description ?: $category->description,
            'keywords' => $category->meta_keywords,
            'canonical' => url()->current(),
            'image' => $category->image ? asset('storage/' . $category->image) : null,
        ];

        return view('categories.show', array_merge($data, compact('category', 'seoData')));
    }

    /**
     * Get category data for AJAX requests.
     */
    public function ajax(string $slug, Request $request)
    {
        $category = Category::active()->where('slug', $slug)->firstOrFail();
        $type = $request->get('type', 'blogs');
        $page = $request->get('page', 1);

        $cacheKey = "category_ajax_{$slug}_{$type}_{$page}";

        $data = Cache::remember($cacheKey, 1800, function () use ($category, $type) {
            if ($type === 'blogs') {
                return Blog::published()
                          ->byCategory($category->slug)
                          ->with(['primaryCategory', 'author'])
                          ->latest('published_at')
                          ->paginate(12);
            } else {
                return Coupon::active()
                            ->where('category_id', $category->id)
                            ->with(['store'])
                            ->ordered()
                            ->paginate(12);
            }
        });

        if ($request->ajax()) {
            $view = $type === 'blogs' ? 'partials.blog-grid' : 'partials.coupon-grid';
            return response()->json([
                'html' => view($view, compact('data'))->render(),
                'hasMore' => $data->hasMorePages(),
                'nextPage' => $data->currentPage() + 1,
            ]);
        }

        return response()->json($data);
    }
}
