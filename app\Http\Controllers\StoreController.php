<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Models\Coupon;
use App\Models\Category;
use App\Models\AffiliateClick;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class StoreController extends Controller
{
    /**
     * Show all stores.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $category = $request->get('category');
        $country = $request->get('country');
        $sort = $request->get('sort', 'popular'); // popular, name, newest
        $page = $request->get('page', 1);

        $cacheKey = 'stores_' . md5($search . $category . $country . $sort . $page);

        $data = Cache::remember($cacheKey, 3600, function () use ($search, $category, $country, $sort) {
            $query = Store::active()->withCount('activeCoupons');

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($category) {
                $query->whereJsonContains('categories', $category);
            }

            if ($country) {
                $query->byCountry($country);
            }

            switch ($sort) {
                case 'name':
                    $query->orderBy('name');
                    break;
                case 'newest':
                    $query->latest();
                    break;
                case 'popular':
                default:
                    $query->orderBy('active_coupons_count', 'desc')
                          ->orderBy('is_featured', 'desc');
                    break;
            }

            $stores = $query->paginate(24);

            // Featured stores
            $featuredStores = Store::active()
                                 ->featured()
                                 ->withCount('activeCoupons')
                                 ->limit(8)
                                 ->get();

            // Available categories and countries for filters
            $categories = Category::active()
                                ->whereHas('coupons')
                                ->pluck('name', 'slug');

            $countries = Store::active()
                            ->distinct()
                            ->pluck('country')
                            ->sort();

            return compact('stores', 'featuredStores', 'categories', 'countries');
        });

        $seoData = [
            'title' => 'Stores - Find Coupons & Deals from Top Brands',
            'description' => 'Browse coupons and deals from hundreds of popular stores and brands. Save money on your favorite products.',
            'canonical' => url('/stores'),
        ];

        return view('stores.index', array_merge($data, compact('seoData')));
    }

    /**
     * Show a specific store with its coupons.
     */
    public function show(string $slug, Request $request): View
    {
        $store = Store::active()->where('slug', $slug)->firstOrFail();
        
        $type = $request->get('type', 'all'); // all, coupons, deals
        $page = $request->get('page', 1);
        
        $cacheKey = "store_{$slug}_{$type}_{$page}";

        $data = Cache::remember($cacheKey, 1800, function () use ($store, $type) {
            $query = $store->activeCoupons()->with(['category']);

            if ($type !== 'all') {
                $query->byType($type);
            }

            $coupons = $query->ordered()->paginate(20);

            // Store statistics
            $stats = [
                'total_coupons' => $store->getActiveCouponsCount(),
                'average_success_rate' => $store->getAverageSuccessRate(),
                'total_clicks' => $store->getTotalClicks(),
                'clicks_this_month' => $store->getClicksThisMonth(),
            ];

            // Related stores
            $relatedStores = Store::active()
                                ->where('id', '!=', $store->id)
                                ->where(function ($query) use ($store) {
                                    if ($store->categories) {
                                        foreach ($store->categories as $category) {
                                            $query->orWhereJsonContains('categories', $category);
                                        }
                                    }
                                })
                                ->withCount('activeCoupons')
                                ->limit(6)
                                ->get();

            return compact('coupons', 'stats', 'relatedStores');
        });

        $seoData = [
            'title' => $store->meta_title,
            'description' => $store->meta_description ?: "Find the best {$store->name} coupons and deals. Save money with verified discount codes.",
            'keywords' => $store->meta_keywords,
            'canonical' => url()->current(),
            'image' => $store->logo ? asset('storage/' . $store->logo) : null,
        ];

        return view('stores.show', array_merge($data, compact('store', 'seoData')));
    }

    /**
     * Redirect to store's affiliate URL and track the click.
     */
    public function visit(string $slug, Request $request): RedirectResponse
    {
        $store = Store::active()->where('slug', $slug)->firstOrFail();

        // Track the affiliate click
        AffiliateClick::track($store, [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'utm_source' => $request->get('utm_source'),
            'utm_medium' => $request->get('utm_medium'),
            'utm_campaign' => $request->get('utm_campaign'),
            'device_type' => $this->getDeviceType($request->userAgent()),
        ]);

        // Add affiliate disclosure parameters if needed
        $affiliateUrl = $store->affiliate_url;
        if ($request->has('utm_source')) {
            $separator = strpos($affiliateUrl, '?') !== false ? '&' : '?';
            $affiliateUrl .= $separator . http_build_query($request->only(['utm_source', 'utm_medium', 'utm_campaign']));
        }

        return redirect()->away($affiliateUrl);
    }

    /**
     * Get device type from user agent.
     */
    private function getDeviceType(string $userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        
        return 'desktop';
    }
}
