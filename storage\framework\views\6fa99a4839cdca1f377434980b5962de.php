<?php $__env->startSection('title', 'Heroes'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Heroes</h1>
            <p class="text-gray-600">Manage homepage hero sections and promotional content</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.settings.heroes')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Settings
            </a>
            <a href="<?php echo e(route('admin.heroes.trash')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
                Trash
            </a>
            <a href="<?php echo e(route('admin.heroes.create')); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Add Hero
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.heroes.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-input">
                            <option value="">All Statuses</option>
                            <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="scheduled" <?php echo e(request('status') === 'scheduled' ? 'selected' : ''); ?>>Scheduled</option>
                            <option value="expired" <?php echo e(request('status') === 'expired' ? 'selected' : ''); ?>>Expired</option>
                        </select>
                    </div>

                    <!-- Text Position Filter -->
                    <div>
                        <label for="text_position" class="form-label">Text Position</label>
                        <select name="text_position" id="text_position" class="form-input">
                            <option value="">All Positions</option>
                            <option value="left" <?php echo e(request('text_position') === 'left' ? 'selected' : ''); ?>>Left</option>
                            <option value="center" <?php echo e(request('text_position') === 'center' ? 'selected' : ''); ?>>Center</option>
                            <option value="right" <?php echo e(request('text_position') === 'right' ? 'selected' : ''); ?>>Right</option>
                        </select>
                    </div>

                    <!-- Text Color Filter -->
                    <div>
                        <label for="text_color" class="form-label">Text Color</label>
                        <select name="text_color" id="text_color" class="form-input">
                            <option value="">All Colors</option>
                            <option value="white" <?php echo e(request('text_color') === 'white' ? 'selected' : ''); ?>>White</option>
                            <option value="dark" <?php echo e(request('text_color') === 'dark' ? 'selected' : ''); ?>>Dark</option>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label for="date_from" class="form-label">Created From</label>
                        <input type="date" name="date_from" id="date_from" class="form-input" value="<?php echo e(request('date_from')); ?>">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date_to" class="form-label">Created To</label>
                        <input type="date" name="date_to" id="date_to" class="form-input" value="<?php echo e(request('date_to')); ?>">
                    </div>
                </div>

                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <button type="submit" class="btn-primary">Apply Filters</button>
                        <a href="<?php echo e(route('admin.heroes.index')); ?>" class="btn-secondary">Clear</a>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <label for="sort_by" class="text-sm text-gray-600">Sort by:</label>
                            <select name="sort_by" id="sort_by" class="form-input w-auto">
                                <option value="sort_order" <?php echo e(request('sort_by', 'sort_order') === 'sort_order' ? 'selected' : ''); ?>>Sort Order</option>
                                <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Created Date</option>
                                <option value="title" <?php echo e(request('sort_by') === 'title' ? 'selected' : ''); ?>>Title</option>
                                <option value="is_active" <?php echo e(request('sort_by') === 'is_active' ? 'selected' : ''); ?>>Status</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label for="sort_direction" class="text-sm text-gray-600">Direction:</label>
                            <select name="sort_direction" id="sort_direction" class="form-input w-auto">
                                <option value="asc" <?php echo e(request('sort_direction', 'asc') === 'asc' ? 'selected' : ''); ?>>Ascending</option>
                                <option value="desc" <?php echo e(request('sort_direction') === 'desc' ? 'selected' : ''); ?>>Descending</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Heroes Table -->
    <div class="card">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hero</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Design</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buttons</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $heroes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hero): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-16 w-24">
                                        <?php if($hero->background_image): ?>
                                            <img class="h-16 w-24 rounded-lg object-cover"
                                                 src="<?php echo e($hero->background_image_url); ?>"
                                                 alt="<?php echo e($hero->title); ?>">
                                        <?php else: ?>
                                            <div class="h-16 w-24 rounded-lg flex items-center justify-center text-white text-xs font-medium"
                                                 style="background: <?php echo e($hero->background_color); ?>">
                                                <?php echo e(substr($hero->title, 0, 2)); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($hero->title); ?></div>
                                        <?php if($hero->subtitle): ?>
                                            <div class="text-sm text-gray-500"><?php echo e(Str::limit($hero->subtitle, 50)); ?></div>
                                        <?php endif; ?>
                                        <div class="text-xs text-gray-400">Created: <?php echo e($hero->created_at->format('M j, Y')); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="space-y-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs bg-gray-100 px-2 py-1 rounded"><?php echo e(ucfirst($hero->text_position)); ?></span>
                                        <span class="text-xs <?php echo e($hero->text_color === 'white' ? 'bg-gray-800 text-white' : 'bg-gray-200 text-gray-800'); ?> px-2 py-1 rounded">
                                            <?php echo e(ucfirst($hero->text_color)); ?>

                                        </span>
                                    </div>
                                    <?php if($hero->background_color): ?>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-4 h-4 rounded border" style="background: <?php echo e($hero->background_color); ?>"></div>
                                            <span class="text-xs text-gray-400">Background</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="space-y-1">
                                    <?php if($hero->primary_button_text): ?>
                                        <div class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                            <?php echo e($hero->primary_button_text); ?>

                                        </div>
                                    <?php endif; ?>
                                    <?php if($hero->secondary_button_text): ?>
                                        <div class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                                            <?php echo e($hero->secondary_button_text); ?>

                                        </div>
                                    <?php endif; ?>
                                    <?php if(!$hero->primary_button_text && !$hero->secondary_button_text): ?>
                                        <span class="text-gray-400">No buttons</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php switch($hero->status):
                                    case ('active'): ?>
                                        <span class="badge-success">Active</span>
                                        <?php break; ?>
                                    <?php case ('inactive'): ?>
                                        <span class="badge-gray">Inactive</span>
                                        <?php break; ?>
                                    <?php case ('scheduled'): ?>
                                        <span class="badge-warning">Scheduled</span>
                                        <?php break; ?>
                                    <?php case ('expired'): ?>
                                        <span class="badge-danger">Expired</span>
                                        <?php break; ?>
                                <?php endswitch; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php if($hero->starts_at): ?>
                                    <div>Start: <?php echo e($hero->starts_at->format('M j, Y')); ?></div>
                                <?php endif; ?>
                                <?php if($hero->expires_at): ?>
                                    <div>End: <?php echo e($hero->expires_at->format('M j, Y')); ?></div>
                                <?php endif; ?>
                                <?php if(!$hero->starts_at && !$hero->expires_at): ?>
                                    <span class="text-gray-400">Always active</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($hero->sort_order); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="<?php echo e(route('admin.heroes.edit', $hero)); ?>"
                                   class="text-blue-600 hover:text-blue-900">Edit</a>

                                <form method="POST" action="<?php echo e(route('admin.heroes.toggle-active', $hero)); ?>" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                            class="<?php echo e($hero->is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'); ?>">
                                        <?php echo e($hero->is_active ? 'Deactivate' : 'Activate'); ?>

                                    </button>
                                </form>

                                <form method="POST" action="<?php echo e(route('admin.heroes.destroy', $hero)); ?>"
                                      class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this hero?')">
                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                <div class="space-y-2">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    <p>No heroes found</p>
                                    <a href="<?php echo e(route('admin.heroes.create')); ?>" class="text-blue-600 hover:text-blue-800">Create your first hero</a>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($heroes->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($heroes->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/admin/heroes/index.blade.php ENDPATH**/ ?>