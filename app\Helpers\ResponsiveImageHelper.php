<?php

namespace App\Helpers;

class ResponsiveImageHelper
{
    /**
     * Generate responsive image HTML with srcset and sizes
     */
    public static function generate(array $imageSizes, string $alt = '', array $options = []): string
    {
        $defaultOptions = [
            'class' => '',
            'loading' => 'lazy',
            'sizes' => [
                '(max-width: 640px) 100vw',
                '(max-width: 1024px) 50vw',
                '33vw'
            ],
            'webp' => true,
            'fallback' => true
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        if (empty($imageSizes)) {
            return self::generatePlaceholder($alt, $options['class']);
        }
        
        // Build srcset arrays
        $webpSrcset = [];
        $originalSrcset = [];
        $fallbackSrc = '';
        
        foreach ($imageSizes as $size => $paths) {
            if (!is_array($paths)) continue;
            
            $width = $paths['width'] ?? null;
            
            if ($options['webp'] && isset($paths['webp']) && $width) {
                $webpSrcset[] = asset('storage/' . $paths['webp']) . ' ' . $width . 'w';
            }
            
            if (isset($paths['original']) && $width) {
                $originalSrcset[] = asset('storage/' . $paths['original']) . ' ' . $width . 'w';
            }
            
            // Set fallback to largest image
            if (isset($paths['original']) && (!$fallbackSrc || $width > 800)) {
                $fallbackSrc = asset('storage/' . $paths['original']);
            }
        }
        
        // Build sizes attribute
        $sizesAttr = is_array($options['sizes']) ? implode(', ', $options['sizes']) : $options['sizes'];
        
        $html = '<picture>';
        
        // WebP source
        if (!empty($webpSrcset) && $options['webp']) {
            $html .= sprintf(
                '<source type="image/webp" srcset="%s" sizes="%s">',
                implode(', ', $webpSrcset),
                $sizesAttr
            );
        }
        
        // Original format source
        if (!empty($originalSrcset)) {
            $html .= sprintf(
                '<source srcset="%s" sizes="%s">',
                implode(', ', $originalSrcset),
                $sizesAttr
            );
        }
        
        // Fallback img tag
        $html .= sprintf(
            '<img src="%s" alt="%s" class="%s" loading="%s">',
            $fallbackSrc ?: self::getPlaceholderUrl(),
            htmlspecialchars($alt),
            $options['class'],
            $options['loading']
        );
        
        $html .= '</picture>';
        
        return $html;
    }
    
    /**
     * Generate simple responsive img tag with srcset
     */
    public static function generateSimple(array $imageSizes, string $alt = '', array $options = []): string
    {
        $defaultOptions = [
            'class' => '',
            'loading' => 'lazy',
            'sizes' => '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        if (empty($imageSizes)) {
            return self::generatePlaceholder($alt, $options['class']);
        }
        
        $srcset = [];
        $fallbackSrc = '';
        
        foreach ($imageSizes as $size => $paths) {
            if (!is_array($paths) || !isset($paths['original'], $paths['width'])) continue;
            
            $srcset[] = asset('storage/' . $paths['original']) . ' ' . $paths['width'] . 'w';
            
            // Set fallback to largest image
            if (!$fallbackSrc || $paths['width'] > 800) {
                $fallbackSrc = asset('storage/' . $paths['original']);
            }
        }
        
        return sprintf(
            '<img src="%s" srcset="%s" sizes="%s" alt="%s" class="%s" loading="%s">',
            $fallbackSrc ?: self::getPlaceholderUrl(),
            implode(', ', $srcset),
            $options['sizes'],
            htmlspecialchars($alt),
            $options['class'],
            $options['loading']
        );
    }
    
    /**
     * Generate placeholder image
     */
    public static function generatePlaceholder(string $alt = '', string $class = ''): string
    {
        return sprintf(
            '<div class="image-placeholder %s" role="img" aria-label="%s">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>',
            $class,
            htmlspecialchars($alt ?: 'Image placeholder')
        );
    }
    
    /**
     * Get placeholder image URL
     */
    public static function getPlaceholderUrl(int $width = 800, int $height = 600): string
    {
        return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='{$width}' height='{$height}' viewBox='0 0 {$width} {$height}'%3E%3Crect width='100%25' height='100%25' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='18' fill='%236b7280'%3EImage%3C/text%3E%3C/svg%3E";
    }
    
    /**
     * Generate progressive image HTML (low-res placeholder + high-res)
     */
    public static function generateProgressive(array $imageSizes, string $alt = '', array $options = []): string
    {
        $defaultOptions = [
            'class' => '',
            'loading' => 'lazy',
            'placeholder' => true
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        if (empty($imageSizes)) {
            return self::generatePlaceholder($alt, $options['class']);
        }
        
        // Find smallest and largest images
        $smallestImage = null;
        $largestImage = null;
        $smallestWidth = PHP_INT_MAX;
        $largestWidth = 0;
        
        foreach ($imageSizes as $size => $paths) {
            if (!is_array($paths) || !isset($paths['width'])) continue;
            
            $width = $paths['width'];
            
            if ($width < $smallestWidth) {
                $smallestWidth = $width;
                $smallestImage = $paths;
            }
            
            if ($width > $largestWidth) {
                $largestWidth = $width;
                $largestImage = $paths;
            }
        }
        
        if (!$largestImage) {
            return self::generatePlaceholder($alt, $options['class']);
        }
        
        $html = '<div class="progressive-image ' . $options['class'] . '">';
        
        // Low-res placeholder
        if ($smallestImage && $options['placeholder']) {
            $html .= sprintf(
                '<img src="%s" alt="" class="placeholder" aria-hidden="true">',
                asset('storage/' . $smallestImage['original'])
            );
        }
        
        // High-res main image
        $html .= sprintf(
            '<img src="%s" alt="%s" class="main-image" loading="%s" onload="this.classList.add(\'loaded\')">',
            asset('storage/' . $largestImage['original']),
            htmlspecialchars($alt),
            $options['loading']
        );
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get optimal image size for given container width
     */
    public static function getOptimalSize(array $imageSizes, int $containerWidth): ?array
    {
        $bestMatch = null;
        $bestDifference = PHP_INT_MAX;
        
        foreach ($imageSizes as $size => $paths) {
            if (!is_array($paths) || !isset($paths['width'])) continue;
            
            $difference = abs($paths['width'] - $containerWidth);
            
            if ($difference < $bestDifference) {
                $bestDifference = $difference;
                $bestMatch = $paths;
            }
        }
        
        return $bestMatch;
    }
    
    /**
     * Generate art direction responsive image
     */
    public static function generateArtDirection(array $breakpoints, string $alt = '', array $options = []): string
    {
        $defaultOptions = [
            'class' => '',
            'loading' => 'lazy'
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        $html = '<picture>';
        
        foreach ($breakpoints as $breakpoint => $imageData) {
            if (is_array($imageData) && isset($imageData['src'])) {
                $media = isset($imageData['media']) ? $imageData['media'] : '';
                $src = asset('storage/' . $imageData['src']);
                
                if ($media) {
                    $html .= sprintf('<source media="%s" srcset="%s">', $media, $src);
                } else {
                    // Default/fallback image
                    $html .= sprintf(
                        '<img src="%s" alt="%s" class="%s" loading="%s">',
                        $src,
                        htmlspecialchars($alt),
                        $options['class'],
                        $options['loading']
                    );
                }
            }
        }
        
        $html .= '</picture>';
        
        return $html;
    }
}
