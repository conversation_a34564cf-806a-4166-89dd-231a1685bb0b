@extends('layouts.admin')

@section('title', 'Edit Hero')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="{{ route('admin.heroes.index') }}" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Hero</h1>
            <p class="text-gray-600">Update hero section details</p>
        </div>
    </div>

    <!-- Current Image Preview -->
    @if($hero->background_image)
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium">Current Background Image</h3>
        </div>
        <div class="card-body">
            <div class="flex items-center space-x-4">
                <img src="{{ $hero->background_image_url }}" alt="Current background" class="w-32 h-20 object-cover rounded-lg">
                <div>
                    <p class="text-sm text-gray-600">Current background image</p>
                    <p class="text-xs text-gray-500">Upload a new image to replace this one</p>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Form -->
    <form action="{{ route('admin.heroes.update', $hero) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="form-input @error('title') border-red-500 @enderror" 
                                   value="{{ old('title', $hero->title) }}" 
                                   required>
                            @error('title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   class="form-input @error('subtitle') border-red-500 @enderror" 
                                   value="{{ old('subtitle', $hero->subtitle) }}" 
                                   placeholder="Optional subtitle">
                            @error('subtitle')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="4" 
                                      class="form-input @error('description') border-red-500 @enderror"
                                      placeholder="Optional description for the hero section">{{ old('description', $hero->description) }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Background Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Background Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="background_image" class="form-label">Background Image</label>
                            <input type="file" 
                                   id="background_image" 
                                   name="background_image" 
                                   accept="image/*" 
                                   class="form-input @error('background_image') border-red-500 @enderror">
                            <p class="text-sm text-gray-500 mt-1">Recommended size: 1920x1080px. Max size: 2MB. Leave empty to keep current image.</p>
                            @error('background_image')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="background_color" class="form-label">Background Color *</label>
                            <div class="flex items-center space-x-2">
                                @php
                                    $bgColor = old('background_color', $hero->background_color);
                                    $isHexColor = preg_match('/^#[0-9A-Fa-f]{6}$/', $bgColor);
                                @endphp
                                <input type="color" 
                                       id="background_color" 
                                       name="background_color_picker" 
                                       class="w-12 h-10 border border-gray-300 rounded cursor-pointer" 
                                       value="{{ $isHexColor ? $bgColor : '#3B82F6' }}">
                                <input type="text" 
                                       id="background_color_text" 
                                       name="background_color"
                                       class="form-input flex-1 @error('background_color') border-red-500 @enderror" 
                                       value="{{ $bgColor }}" 
                                       placeholder="#3B82F6 or linear-gradient(...)"
                                       required>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Use hex color or CSS gradient (e.g., linear-gradient(135deg, #667eea 0%, #764ba2 100%))</p>
                            @error('background_color')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Button Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Button Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <!-- Primary Button -->
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Primary Button</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="primary_button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           id="primary_button_text" 
                                           name="primary_button_text" 
                                           class="form-input @error('primary_button_text') border-red-500 @enderror" 
                                           value="{{ old('primary_button_text', $hero->primary_button_text) }}" 
                                           placeholder="Get Started">
                                    @error('primary_button_text')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="primary_button_url" class="form-label">Button URL</label>
                                    <input type="url" 
                                           id="primary_button_url" 
                                           name="primary_button_url" 
                                           class="form-input @error('primary_button_url') border-red-500 @enderror" 
                                           value="{{ old('primary_button_url', $hero->primary_button_url) }}" 
                                           placeholder="https://example.com">
                                    @error('primary_button_url')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="primary_button_style" class="form-label">Button Style *</label>
                                <select id="primary_button_style" 
                                        name="primary_button_style" 
                                        class="form-input @error('primary_button_style') border-red-500 @enderror" 
                                        required>
                                    <option value="primary" {{ old('primary_button_style', $hero->primary_button_style) === 'primary' ? 'selected' : '' }}>Primary (Gradient)</option>
                                    <option value="secondary" {{ old('primary_button_style', $hero->primary_button_style) === 'secondary' ? 'selected' : '' }}>Secondary (Solid)</option>
                                    <option value="outline" {{ old('primary_button_style', $hero->primary_button_style) === 'outline' ? 'selected' : '' }}>Outline</option>
                                </select>
                                @error('primary_button_style')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Secondary Button -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">Secondary Button</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="secondary_button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           id="secondary_button_text" 
                                           name="secondary_button_text" 
                                           class="form-input @error('secondary_button_text') border-red-500 @enderror" 
                                           value="{{ old('secondary_button_text', $hero->secondary_button_text) }}" 
                                           placeholder="Learn More">
                                    @error('secondary_button_text')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="secondary_button_url" class="form-label">Button URL</label>
                                    <input type="url" 
                                           id="secondary_button_url" 
                                           name="secondary_button_url" 
                                           class="form-input @error('secondary_button_url') border-red-500 @enderror" 
                                           value="{{ old('secondary_button_url', $hero->secondary_button_url) }}" 
                                           placeholder="https://example.com">
                                    @error('secondary_button_url')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="secondary_button_style" class="form-label">Button Style *</label>
                                <select id="secondary_button_style" 
                                        name="secondary_button_style" 
                                        class="form-input @error('secondary_button_style') border-red-500 @enderror" 
                                        required>
                                    <option value="primary" {{ old('secondary_button_style', $hero->secondary_button_style) === 'primary' ? 'selected' : '' }}>Primary (Gradient)</option>
                                    <option value="secondary" {{ old('secondary_button_style', $hero->secondary_button_style) === 'secondary' ? 'selected' : '' }}>Secondary (Solid)</option>
                                    <option value="outline" {{ old('secondary_button_style', $hero->secondary_button_style) === 'outline' ? 'selected' : '' }}>Outline</option>
                                </select>
                                @error('secondary_button_style')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="text_position" class="form-label">Text Position *</label>
                            <select id="text_position" 
                                    name="text_position" 
                                    class="form-input @error('text_position') border-red-500 @enderror" 
                                    required>
                                <option value="left" {{ old('text_position', $hero->text_position) === 'left' ? 'selected' : '' }}>Left</option>
                                <option value="center" {{ old('text_position', $hero->text_position) === 'center' ? 'selected' : '' }}>Center</option>
                                <option value="right" {{ old('text_position', $hero->text_position) === 'right' ? 'selected' : '' }}>Right</option>
                            </select>
                            @error('text_position')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="text_color" class="form-label">Text Color *</label>
                            <select id="text_color" 
                                    name="text_color" 
                                    class="form-input @error('text_color') border-red-500 @enderror" 
                                    required>
                                <option value="white" {{ old('text_color', $hero->text_color) === 'white' ? 'selected' : '' }}>White</option>
                                <option value="dark" {{ old('text_color', $hero->text_color) === 'dark' ? 'selected' : '' }}>Dark</option>
                            </select>
                            @error('text_color')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input @error('sort_order') border-red-500 @enderror" 
                                   value="{{ old('sort_order', $hero->sort_order) }}" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Schedule Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Schedule Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="starts_at" class="form-label">Start Date</label>
                            <input type="datetime-local" 
                                   id="starts_at" 
                                   name="starts_at" 
                                   class="form-input @error('starts_at') border-red-500 @enderror" 
                                   value="{{ old('starts_at', $hero->starts_at ? $hero->starts_at->format('Y-m-d\TH:i') : '') }}">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to start immediately</p>
                            @error('starts_at')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="expires_at" class="form-label">End Date</label>
                            <input type="datetime-local" 
                                   id="expires_at" 
                                   name="expires_at" 
                                   class="form-input @error('expires_at') border-red-500 @enderror" 
                                   value="{{ old('expires_at', $hero->expires_at ? $hero->expires_at->format('Y-m-d\TH:i') : '') }}">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to never expire</p>
                            @error('expires_at')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ old('is_active', $hero->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Preview</h3>
                    </div>
                    <div class="card-body">
                        <div id="hero-preview" class="rounded-lg p-8 text-center min-h-[200px] flex flex-col justify-center" 
                             style="background: {{ $hero->background_color }}; color: {{ $hero->text_color === 'dark' ? '#1F2937' : '#FFFFFF' }}; text-align: {{ $hero->text_position }};">
                            <h2 id="preview-title" class="text-2xl font-bold mb-2">{{ $hero->title }}</h2>
                            @if($hero->subtitle)
                                <p id="preview-subtitle" class="text-lg mb-4 opacity-90">{{ $hero->subtitle }}</p>
                            @else
                                <p id="preview-subtitle" class="text-lg mb-4 opacity-90" style="display: none;">Hero Subtitle</p>
                            @endif
                            @if($hero->description)
                                <p id="preview-description" class="mb-6 opacity-80">{{ $hero->description }}</p>
                            @else
                                <p id="preview-description" class="mb-6 opacity-80" style="display: none;">Hero Description</p>
                            @endif
                            <div id="preview-buttons" class="flex justify-center space-x-4">
                                <div id="preview-primary-btn" class="px-6 py-2 rounded-lg bg-white text-blue-600 font-medium" style="display: {{ $hero->primary_button_text ? 'block' : 'none' }};">
                                    {{ $hero->primary_button_text ?: 'Primary Button' }}
                                </div>
                                <div id="preview-secondary-btn" class="px-6 py-2 rounded-lg border-2 border-white font-medium" style="display: {{ $hero->secondary_button_text ? 'block' : 'none' }};">
                                    {{ $hero->secondary_button_text ?: 'Secondary Button' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.heroes.index') }}" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Hero</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('title');
    const subtitleInput = document.getElementById('subtitle');
    const descriptionInput = document.getElementById('description');
    const backgroundColorInput = document.getElementById('background_color_text');
    const textColorInput = document.getElementById('text_color');
    const textPositionInput = document.getElementById('text_position');
    const primaryButtonTextInput = document.getElementById('primary_button_text');
    const secondaryButtonTextInput = document.getElementById('secondary_button_text');
    const primaryButtonStyleInput = document.getElementById('primary_button_style');
    const secondaryButtonStyleInput = document.getElementById('secondary_button_style');

    const preview = document.getElementById('hero-preview');
    const previewTitle = document.getElementById('preview-title');
    const previewSubtitle = document.getElementById('preview-subtitle');
    const previewDescription = document.getElementById('preview-description');
    const previewPrimaryBtn = document.getElementById('preview-primary-btn');
    const previewSecondaryBtn = document.getElementById('preview-secondary-btn');

    // Color picker sync
    document.getElementById('background_color').addEventListener('input', function() {
        document.getElementById('background_color_text').value = this.value;
        updatePreview();
    });

    // Update preview function
    function updatePreview() {
        previewTitle.textContent = titleInput.value || 'Hero Title';
        previewSubtitle.textContent = subtitleInput.value || 'Hero Subtitle';
        previewDescription.textContent = descriptionInput.value || 'Hero Description';
        
        // Update background
        preview.style.background = backgroundColorInput.value || '#3B82F6';
        
        // Update text color
        const textColor = textColorInput.value === 'dark' ? '#1F2937' : '#FFFFFF';
        preview.style.color = textColor;
        
        // Update text position
        const textAlign = textPositionInput.value || 'center';
        preview.style.textAlign = textAlign;
        
        // Update buttons
        if (primaryButtonTextInput.value) {
            previewPrimaryBtn.textContent = primaryButtonTextInput.value;
            previewPrimaryBtn.style.display = 'block';

            // Apply button style
            const primaryStyle = primaryButtonStyleInput.value;
            previewPrimaryBtn.className = 'px-6 py-2 rounded-lg font-medium';
            if (primaryStyle === 'primary') {
                previewPrimaryBtn.className += ' bg-gradient-to-r from-blue-500 to-purple-600 text-white';
            } else if (primaryStyle === 'secondary') {
                previewPrimaryBtn.className += ' bg-white text-blue-600';
            } else if (primaryStyle === 'outline') {
                previewPrimaryBtn.className += ' border-2 border-white text-white bg-transparent';
            }
        } else {
            previewPrimaryBtn.style.display = 'none';
        }

        if (secondaryButtonTextInput.value) {
            previewSecondaryBtn.textContent = secondaryButtonTextInput.value;
            previewSecondaryBtn.style.display = 'block';

            // Apply button style
            const secondaryStyle = secondaryButtonStyleInput.value;
            previewSecondaryBtn.className = 'px-6 py-2 rounded-lg font-medium';
            if (secondaryStyle === 'primary') {
                previewSecondaryBtn.className += ' bg-gradient-to-r from-blue-500 to-purple-600 text-white';
            } else if (secondaryStyle === 'secondary') {
                previewSecondaryBtn.className += ' bg-white text-blue-600';
            } else if (secondaryStyle === 'outline') {
                previewSecondaryBtn.className += ' border-2 border-white text-white bg-transparent';
            }
        } else {
            previewSecondaryBtn.style.display = 'none';
        }
        
        // Hide subtitle and description if empty
        previewSubtitle.style.display = subtitleInput.value ? 'block' : 'none';
        previewDescription.style.display = descriptionInput.value ? 'block' : 'none';
    }

    // Add event listeners
    [titleInput, subtitleInput, descriptionInput, backgroundColorInput, textColorInput, textPositionInput, primaryButtonTextInput, secondaryButtonTextInput, primaryButtonStyleInput, secondaryButtonStyleInput].forEach(input => {
        if (input) {
            input.addEventListener('input', updatePreview);
            input.addEventListener('change', updatePreview); // For select elements
        }
    });

    // Sync text input with color picker
    backgroundColorInput.addEventListener('input', function() {
        if (this.value.startsWith('#') && this.value.length === 7) {
            document.getElementById('background_color').value = this.value;
        }
    });
});
</script>
@endsection
