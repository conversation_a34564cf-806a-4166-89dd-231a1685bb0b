@extends('layouts.admin')

@section('title', 'Edit Category')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="{{ route('admin.categories.index') }}" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Category</h1>
            <p class="text-gray-600">Update category information</p>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('admin.categories.update', $category) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="name" class="form-label">Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   class="form-input @error('name') border-red-500 @enderror" 
                                   value="{{ old('name', $category->name) }}" 
                                   required>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3" 
                                      class="form-input @error('description') border-red-500 @enderror"
                                      placeholder="Optional description for the category">{{ old('description', $category->description) }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="image" class="form-label">Category Image</label>
                            @if($category->image)
                                <div class="mb-3">
                                    <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-20 h-20 object-cover rounded-lg">
                                </div>
                            @endif
                            <input type="file" 
                                   id="image" 
                                   name="image" 
                                   accept="image/*" 
                                   class="form-input @error('image') border-red-500 @enderror">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to keep current image. Max size: 2MB</p>
                            @error('image')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="parent_id" class="form-label">Parent Category</label>
                            <select id="parent_id" 
                                    name="parent_id" 
                                    class="form-input @error('parent_id') border-red-500 @enderror">
                                <option value="">None (Top Level)</option>
                                @foreach(\App\Models\Category::active()->parent()->where('id', '!=', $category->id)->get() as $parentCategory)
                                    <option value="{{ $parentCategory->id }}" {{ old('parent_id', $category->parent_id) == $parentCategory->id ? 'selected' : '' }}>
                                        {{ $parentCategory->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('parent_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input @error('sort_order') border-red-500 @enderror" 
                                   value="{{ old('sort_order', $category->sort_order) }}" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="show_in_menu" 
                                   name="show_in_menu" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ old('show_in_menu', $category->show_in_menu ?? true) ? 'checked' : '' }}>
                            <label for="show_in_menu" class="ml-2 text-sm text-gray-700">Show in Navigation</label>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $category->blogs()->published()->count() }}</div>
                                <div class="text-sm text-gray-500">Published Blogs</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">{{ $category->created_at->format('M Y') }}</div>
                                <div class="text-sm text-gray-500">Created</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">SEO</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" 
                                   id="meta_title" 
                                   name="meta_title" 
                                   class="form-input @error('meta_title') border-red-500 @enderror" 
                                   value="{{ old('meta_title', $category->meta_title) }}"
                                   maxlength="60">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to use category name</p>
                            @error('meta_title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea id="meta_description" 
                                      name="meta_description" 
                                      rows="2" 
                                      class="form-input @error('meta_description') border-red-500 @enderror"
                                      maxlength="160">{{ old('meta_description', $category->meta_description) }}</textarea>
                            <p class="text-sm text-gray-500 mt-1">Recommended: 150-160 characters</p>
                            @error('meta_description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.categories.index') }}" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Category</button>
        </div>
    </form>
</div>
@endsection
