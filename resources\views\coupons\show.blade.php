@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumbs -->
    <nav class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700">Home</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('coupons.index') }}" class="text-gray-500 hover:text-gray-700">Coupons</a></li>
                @if($coupon->category ?? false)
                    <li><span class="text-gray-400">/</span></li>
                    <li><a href="{{ route('coupons.index', ['category' => $coupon->category->slug]) }}" class="text-gray-500 hover:text-gray-700">{{ $coupon->category->name }}</a></li>
                @endif
                <li><span class="text-gray-400">/</span></li>
                <li class="text-gray-900 font-medium">{{ $coupon->title ?? 'Coupon Details' }}</li>
            </ol>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Coupon Header -->
                <div class="card mb-8">
                    <div class="p-8">
                        <!-- Store Info -->
                        <div class="flex items-center mb-6">
                            @if($coupon->store->logo ?? false)
                                <img src="{{ asset('storage/' . $coupon->store->logo) }}" 
                                     alt="{{ $coupon->store->name }}" 
                                     class="w-16 h-16 rounded-lg object-cover mr-4">
                            @else
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                                    <span class="text-white font-bold text-2xl">{{ substr($coupon->store->name ?? 'S', 0, 1) }}</span>
                                </div>
                            @endif
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">{{ $coupon->store->name ?? 'Sample Store' }}</h2>
                                @if($coupon->category ?? false)
                                    <p class="text-gray-600">{{ $coupon->category->name }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Coupon Title -->
                        <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                            {{ $coupon->title ?? 'Amazing Deal - Save Big Today!' }}
                        </h1>

                        <!-- Discount Badge -->
                        @if($coupon->discount_display ?? false)
                            <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xl font-bold mb-6">
                                {{ $coupon->discount_display }}
                            </div>
                        @else
                            <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full text-xl font-bold mb-6">
                                50% OFF
                            </div>
                        @endif

                        <!-- Description -->
                        <p class="text-lg text-gray-700 mb-6 leading-relaxed">
                            {{ $coupon->description ?? 'Get incredible savings on your favorite products with this exclusive deal. Limited time offer - don\'t miss out on these amazing discounts!' }}
                        </p>

                        <!-- Coupon Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            @if($coupon->code ?? false)
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2">Coupon Code</h3>
                                    <div class="flex items-center space-x-3">
                                        <code class="coupon-code bg-white px-4 py-2 rounded border text-lg font-mono font-bold" 
                                              data-code="{{ $coupon->code }}">
                                            {{ $coupon->code }}
                                        </code>
                                        <button class="copy-code-btn btn-outline text-sm" data-code="{{ $coupon->code }}">
                                            Copy Code
                                        </button>
                                    </div>
                                </div>
                            @else
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2">Coupon Code</h3>
                                    <div class="flex items-center space-x-3">
                                        <code class="coupon-code bg-white px-4 py-2 rounded border text-lg font-mono font-bold">
                                            SAVE50
                                        </code>
                                        <button class="copy-code-btn btn-outline text-sm" data-code="SAVE50">
                                            Copy Code
                                        </button>
                                    </div>
                                </div>
                            @endif

                            @if($coupon->expires_at ?? false)
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2">Expires</h3>
                                    <p class="text-lg {{ $coupon->expires_in_days <= 3 ? 'text-red-600 font-semibold' : 'text-gray-700' }}">
                                        {{ $coupon->expires_at->format('M j, Y') }}
                                        @if($coupon->expires_in_days > 0)
                                            <span class="text-sm text-gray-500">({{ $coupon->expires_in_days }} days left)</span>
                                        @endif
                                    </p>
                                </div>
                            @else
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2">Expires</h3>
                                    <p class="text-lg text-gray-700">
                                        December 31, 2024
                                        <span class="text-sm text-gray-500">(16 days left)</span>
                                    </p>
                                </div>
                            @endif
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="{{ route('coupons.visit', $coupon->slug ?? 'sample') }}" 
                               target="_blank"
                               class="flex-1 btn-primary text-center text-lg py-4"
                               onclick="trackCouponClick('{{ $coupon->slug ?? 'sample' }}')">
                                @if($coupon->code ?? false)
                                    Get Code & Visit {{ $coupon->store->name ?? 'Store' }}
                                @else
                                    Get Deal & Visit Store
                                @endif
                            </a>
                            <button class="btn-outline px-8 py-4" onclick="markAsUsed()">
                                Mark as Used
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Terms & Conditions -->
                <div class="card mb-8">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Terms & Conditions</h3>
                        @if($coupon->terms ?? false)
                            <div class="prose prose-gray max-w-none">
                                {!! nl2br(e($coupon->terms)) !!}
                            </div>
                        @else
                            <div class="prose prose-gray max-w-none">
                                <ul class="space-y-2">
                                    <li>Valid for new and existing customers</li>
                                    <li>Cannot be combined with other offers</li>
                                    <li>Minimum purchase may apply</li>
                                    <li>Offer valid while supplies last</li>
                                    <li>Standard shipping rates apply</li>
                                    <li>Excludes sale items and gift cards</li>
                                </ul>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- How to Use -->
                <div class="card">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">How to Use This Coupon</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <span class="text-blue-600 font-bold text-lg">1</span>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Copy Code</h4>
                                <p class="text-sm text-gray-600">Click the "Copy Code" button to copy the coupon code to your clipboard.</p>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <span class="text-green-600 font-bold text-lg">2</span>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Visit Store</h4>
                                <p class="text-sm text-gray-600">Click "Visit Store" to go to the retailer's website and shop for your items.</p>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <span class="text-purple-600 font-bold text-lg">3</span>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Apply & Save</h4>
                                <p class="text-sm text-gray-600">Paste the code at checkout to apply your discount and enjoy the savings!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Coupon Stats -->
                <div class="card">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Coupon Stats</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Success Rate</span>
                                <span class="font-semibold text-green-600">{{ number_format($coupon->success_rate ?? 85, 0) }}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Used by</span>
                                <span class="font-semibold">{{ number_format($coupon->usage_count ?? 1247) }} people</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Added</span>
                                <span class="font-semibold">{{ $coupon->created_at->format('M j, Y') ?? 'Dec 1, 2024' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Store Info -->
                <div class="card">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">About {{ $coupon->store->name ?? 'This Store' }}</h3>
                        @if($coupon->store->description ?? false)
                            <p class="text-gray-600 mb-4">{{ Str::limit($coupon->store->description, 150) }}</p>
                        @else
                            <p class="text-gray-600 mb-4">Discover amazing deals and exclusive offers from one of the most trusted retailers online.</p>
                        @endif
                        
                        <div class="space-y-3">
                            @if($coupon->store->rating ?? false)
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Rating</span>
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            <svg class="w-4 h-4 {{ $i <= $coupon->store->rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                                                 fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endfor
                                        <span class="ml-2 text-sm text-gray-600">{{ number_format($coupon->store->rating, 1) }}</span>
                                    </div>
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Active Deals</span>
                                <span class="font-semibold">{{ $coupon->store->activeCoupons->count() ?? 12 }}</span>
                            </div>
                        </div>
                        
                        <a href="{{ route('stores.show', $coupon->store->slug ?? 'sample') }}" 
                           class="btn-outline w-full mt-4">
                            View All Deals
                        </a>
                    </div>
                </div>

                <!-- Share -->
                <div class="card">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Share This Deal</h3>
                        <div class="flex space-x-3">
                            <a href="#" class="flex-1 btn-outline text-center text-sm">
                                Facebook
                            </a>
                            <a href="#" class="flex-1 btn-outline text-center text-sm">
                                Twitter
                            </a>
                            <a href="#" class="flex-1 btn-outline text-center text-sm">
                                Copy Link
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Coupons -->
    @if(isset($relatedCoupons) && $relatedCoupons->count() > 0)
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Deals</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($relatedCoupons as $relatedCoupon)
                    @include('components.coupon-card', ['coupon' => $relatedCoupon])
                @endforeach
            </div>
        </div>
    </section>
    @endif
</div>

<script>
function trackCouponClick(slug) {
    fetch(`/coupons/${slug}/visit`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    });
}

function markAsUsed() {
    const slug = '{{ $coupon->slug ?? "sample" }}';
    fetch(`/coupons/${slug}/success`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    }).then(response => response.json())
      .then(data => {
          alert('Thank you for your feedback!');
      });
}

// Copy code functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.copy-code-btn').forEach(button => {
        button.addEventListener('click', function() {
            const code = this.getAttribute('data-code');
            navigator.clipboard.writeText(code).then(() => {
                this.textContent = 'Copied!';
                this.classList.add('bg-green-600', 'text-white');
                setTimeout(() => {
                    this.textContent = 'Copy Code';
                    this.classList.remove('bg-green-600', 'text-white');
                }, 2000);
            });
        });
    });
});
</script>
@endsection
