<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class BannerSettingsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Banner section settings
        Setting::set(
            'banner_section_enabled',
            true,
            'boolean',
            'banners',
            'Enable or disable the entire banner section on homepage'
        );

        Setting::set(
            'banner_section_title',
            'Featured Promotions',
            'string',
            'banners',
            'Title for the banner section on homepage'
        );

        Setting::set(
            'banner_section_subtitle',
            'Discover amazing deals and offers from our partners',
            'string',
            'banners',
            'Subtitle for the banner section on homepage'
        );

        Setting::set(
            'banner_auto_hide_when_empty',
            true,
            'boolean',
            'banners',
            'Automatically hide banner section when no active banners are available'
        );

        Setting::set(
            'banner_max_display',
            5,
            'integer',
            'banners',
            'Maximum number of banners to display on homepage'
        );
    }
}
