@extends('layouts.admin')

@section('title', 'Edit Tag')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="{{ route('admin.tags.index') }}" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Tag</h1>
            <p class="text-gray-600">Update tag information</p>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('admin.tags.update', $tag) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="name" class="form-label">Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   class="form-input @error('name') border-red-500 @enderror" 
                                   value="{{ old('name', $tag->name) }}" 
                                   required>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3" 
                                      class="form-input @error('description') border-red-500 @enderror"
                                      placeholder="Optional description for the tag">{{ old('description', $tag->description) }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="color" class="form-label">Color *</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" 
                                       id="color" 
                                       name="color" 
                                       class="w-12 h-10 border border-gray-300 rounded cursor-pointer @error('color') border-red-500 @enderror" 
                                       value="{{ old('color', $tag->color) }}" 
                                       required>
                                <input type="text" 
                                       class="form-input flex-1" 
                                       value="{{ old('color', $tag->color) }}" 
                                       readonly>
                            </div>
                            @error('color')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input @error('sort_order') border-red-500 @enderror" 
                                   value="{{ old('sort_order', $tag->sort_order) }}" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ old('is_active', $tag->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $tag->getActiveBlogsCount() }}</div>
                                <div class="text-sm text-gray-500">Active Blogs</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">{{ $tag->created_at->format('M Y') }}</div>
                                <div class="text-sm text-gray-500">Created</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.tags.index') }}" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Tag</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorText = colorInput.nextElementSibling;
    
    colorInput.addEventListener('input', function() {
        colorText.value = this.value;
    });
    
    colorText.addEventListener('input', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            colorInput.value = this.value;
        }
    });
});
</script>
@endsection
