<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

class AffiliateClick extends Model
{
    use HasFactory;

    protected $fillable = [
        'clickable_type',
        'clickable_id',
        'ip_address',
        'user_agent',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'country',
        'device_type',
        'clicked_at',
    ];

    protected $casts = [
        'clicked_at' => 'datetime',
    ];

    // Relationships
    public function clickable(): MorphTo
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('clicked_at', today());
    }

    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('clicked_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('clicked_at', now()->month)
                    ->whereYear('clicked_at', now()->year);
    }

    public function scopeByCountry(Builder $query, string $country): Builder
    {
        return $query->where('country', $country);
    }

    public function scopeByDevice(Builder $query, string $deviceType): Builder
    {
        return $query->where('device_type', $deviceType);
    }

    public function scopeByUtmSource(Builder $query, string $source): Builder
    {
        return $query->where('utm_source', $source);
    }

    // Methods
    public static function track($clickable, array $data = []): self
    {
        return self::create(array_merge([
            'clickable_type' => get_class($clickable),
            'clickable_id' => $clickable->id,
            'clicked_at' => now(),
        ], $data));
    }

    public function getDeviceTypeFromUserAgent(string $userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        
        return 'desktop';
    }
}
