// Service Worker for BlogHub
// Handles caching of assets and offline functionality

const CACHE_NAME = "bloghub-v1";
const STATIC_CACHE = "bloghub-static-v1";
const DYNAMIC_CACHE = "bloghub-dynamic-v1";
const IMAGE_CACHE = "bloghub-images-v1";

// Assets to cache immediately
const STATIC_ASSETS = [
    "/",
    "/blog",
    "/about",
    "/contact",
    "/offline.html",
    // Add your main CSS and JS files here
    // These will be populated by Vite build process
];

// Install event - cache static assets
self.addEventListener("install", (event) => {
    console.log("Service Worker: Installing...");

    event.waitUntil(
        caches
            .open(STATIC_CACHE)
            .then((cache) => {
                console.log("Service Worker: Caching static assets");
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log("Service Worker: Static assets cached");
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error(
                    "Service Worker: Failed to cache static assets",
                    error
                );
            })
    );
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
    console.log("Service Worker: Activating...");

    event.waitUntil(
        caches
            .keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (
                            cacheName !== STATIC_CACHE &&
                            cacheName !== DYNAMIC_CACHE &&
                            cacheName !== IMAGE_CACHE
                        ) {
                            console.log(
                                "Service Worker: Deleting old cache",
                                cacheName
                            );
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log("Service Worker: Activated");
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when possible
self.addEventListener("fetch", (event) => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== "GET") {
        return;
    }

    // Skip admin routes
    if (url.pathname.startsWith("/admin")) {
        return;
    }

    // Handle different types of requests
    if (request.destination === "image") {
        event.respondWith(handleImageRequest(request));
    } else if (isStaticAsset(request)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(handleAPIRequest(request));
    } else {
        event.respondWith(handlePageRequest(request));
    }
});

// Handle image requests with caching
async function handleImageRequest(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(IMAGE_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error("Service Worker: Image request failed", error);
        // Return a placeholder image
        return new Response(
            '<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="100%" height="100%" fill="#f3f4f6"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="18" fill="#6b7280">Image unavailable</text></svg>',
            { headers: { "Content-Type": "image/svg+xml" } }
        );
    }
}

// Handle static assets (CSS, JS, fonts)
async function handleStaticAsset(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error("Service Worker: Static asset request failed", error);
        throw error;
    }
}

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error(
            "Service Worker: API request failed, trying cache",
            error
        );
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// Handle page requests
async function handlePageRequest(request) {
    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error(
            "Service Worker: Page request failed, trying cache",
            error
        );

        // Try to serve from cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        // Serve offline page for navigation requests
        if (request.mode === "navigate") {
            const offlineResponse = await caches.match("/offline.html");
            if (offlineResponse) {
                return offlineResponse;
            }
        }

        throw error;
    }
}

// Helper functions
function isStaticAsset(request) {
    const url = new URL(request.url);
    return url.pathname.match(
        /\.(css|js|woff2?|ttf|eot|ico|png|jpg|jpeg|gif|svg|webp)$/
    );
}

function isAPIRequest(request) {
    const url = new URL(request.url);
    return (
        url.pathname.startsWith("/api/") ||
        url.pathname.startsWith("/ajax/") ||
        request.headers.get("Accept")?.includes("application/json")
    );
}

// Background sync for form submissions
self.addEventListener("sync", (event) => {
    if (event.tag === "background-sync") {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // Handle queued form submissions when back online
    console.log("Service Worker: Performing background sync");
}

// Push notifications (if needed)
self.addEventListener("push", (event) => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: "/icon-192x192.png",
            badge: "/badge-72x72.png",
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey,
            },
            actions: [
                {
                    action: "explore",
                    title: "Read More",
                    icon: "/icon-explore.png",
                },
                {
                    action: "close",
                    title: "Close",
                    icon: "/icon-close.png",
                },
            ],
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Handle notification clicks
self.addEventListener("notificationclick", (event) => {
    event.notification.close();

    if (event.action === "explore") {
        event.waitUntil(clients.openWindow("/blog"));
    }
});

// Clean up old caches periodically
self.addEventListener("message", (event) => {
    if (event.data && event.data.type === "SKIP_WAITING") {
        self.skipWaiting();
    }

    if (event.data && event.data.type === "CLEAN_CACHE") {
        event.waitUntil(cleanOldCaches());
    }
});

async function cleanOldCaches() {
    const cacheNames = await caches.keys();
    const oldCaches = cacheNames.filter(
        (name) =>
            !name.includes("v1") &&
            (name.includes("bloghub") ||
                name.includes("static") ||
                name.includes("dynamic"))
    );

    return Promise.all(oldCaches.map((cacheName) => caches.delete(cacheName)));
}
