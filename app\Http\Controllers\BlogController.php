<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Spatie\ResponseCache\Facades\ResponseCache;

class BlogController extends Controller
{
    /**
     * Show all blogs (paginated).
     */
    public function index(Request $request): View
    {
        $page = $request->get('page', 1);
        $category = $request->get('category');
        $tag = $request->get('tag');
        $search = $request->get('search');

        $cacheKey = 'blogs_' . md5($page . $category . $tag . $search);

        $data = Cache::remember($cacheKey, 3600, function () use ($category, $tag, $search) {
            $query = Blog::published()
                        ->with(['primaryCategory', 'author', 'tags'])
                        ->latest('published_at');

            if ($category) {
                $query->byCategory($category);
            }

            if ($tag) {
                $query->byTag($tag);
            }

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
                });
            }

            $blogs = $query->paginate(12);
            $featuredBlogs = Blog::published()->featured()->limit(3)->get();
            $categories = Category::active()->withCount('blogs')->get();
            $tags = Tag::active()->popular()->limit(20)->get();

            return compact('blogs', 'featuredBlogs', 'categories', 'tags');
        });

        $seoData = [
            'title' => 'Blog - Latest Articles & Insights',
            'description' => 'Discover the latest articles, tips, and insights on our blog.',
            'canonical' => url()->current(),
        ];

        // Add filter data
        $data['currentCategory'] = $category;
        $data['currentTag'] = $tag;
        $data['currentSearch'] = $search;

        return view('blog.index', array_merge($data, compact('seoData')));
    }

    /**
     * Show a single blog post.
     */
    public function show(string $slug): View
    {
        $blog = Cache::remember("blog_{$slug}", 3600, function () use ($slug) {
            return Blog::published()
                      ->with(['primaryCategory', 'categories', 'author'])
                      ->where('slug', $slug)
                      ->firstOrFail();
        });

        // Increment views
        $blog->incrementViews();

        // Get related blogs
        $relatedBlogs = Cache::remember("related_blogs_{$slug}", 3600, function () use ($blog) {
            return Blog::published()
                      ->where('id', '!=', $blog->id)
                      ->where(function ($query) use ($blog) {
                          if ($blog->category_id) {
                              $query->where('category_id', $blog->category_id);
                          }
                      })
                      ->limit(4)
                      ->get();
        });

        $seoData = [
            'title' => $blog->meta_title,
            'description' => $blog->meta_description ?: $blog->excerpt,
            'keywords' => $blog->meta_keywords,
            'canonical' => $blog->canonical_url ?: url()->current(),
            'image' => $blog->featured_image ? asset('storage/' . $blog->featured_image) : null,
            'type' => 'article',
            'published_time' => $blog->published_at?->toISOString(),
            'modified_time' => $blog->updated_at->toISOString(),
            'author' => $blog->author?->name,
        ];

        return view('blog.show', compact('blog', 'relatedBlogs', 'seoData'));
    }

    /**
     * Show blogs filtered by tag.
     */
    public function tag(string $slug, Request $request): View
    {
        $tag = Tag::active()->where('slug', $slug)->firstOrFail();

        $page = $request->get('page', 1);
        $cacheKey = "blogs_tag_{$slug}_{$page}";

        $data = Cache::remember($cacheKey, 3600, function () use ($tag) {
            $blogs = Blog::published()
                        ->byTag($tag->slug)
                        ->with(['primaryCategory', 'categories', 'author', 'tags'])
                        ->latest('published_at')
                        ->paginate(12);

            $categories = Category::active()
                                ->whereHas('blogs', function ($query) {
                                    $query->published();
                                })
                                ->withCount(['blogs' => function ($query) {
                                    $query->published();
                                }])
                                ->ordered()
                                ->get();

            return compact('blogs', 'categories');
        });

        $seoData = [
            'title' => "Articles tagged with '{$tag->name}' - " . config('app.name'),
            'description' => $tag->description ?: "Browse all articles tagged with {$tag->name}.",
            'canonical' => url()->current(),
        ];

        return view('blog.tag', array_merge($data, compact('tag', 'seoData')));
    }

    /**
     * Show blogs by category.
     */
    public function category(string $categorySlug): View
    {
        $category = Category::active()->where('slug', $categorySlug)->firstOrFail();

        $page = request('page', 1);
        $cacheKey = "category_blogs_{$categorySlug}_{$page}";

        $blogs = Cache::remember($cacheKey, 3600, function () use ($category) {
            return Blog::published()
                      ->byCategory($category->slug)
                      ->with(['primaryCategory', 'author'])
                      ->latest('published_at')
                      ->paginate(12);
        });

        $seoData = [
            'title' => $category->meta_title,
            'description' => $category->meta_description ?: $category->description,
            'keywords' => $category->meta_keywords,
            'canonical' => url()->current(),
        ];

        return view('blog.category', compact('category', 'blogs', 'seoData'));
    }

    public function destroy(Blog $blog)
    {
        $slug = $blog->slug;
        $blog->delete();

        // Only clear that blog’s cache
        Cache::forget('blog_' . $slug);

        ResponseCache::clear();

        return redirect()->route('blog.index')->with('success', 'Blog deleted successfully');
    }
}
