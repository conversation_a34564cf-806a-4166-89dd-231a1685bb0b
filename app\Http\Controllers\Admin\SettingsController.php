<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    /**
     * Show banner settings.
     */
    public function banners(): View
    {
        $settings = [
            'banner_section_enabled' => Setting::get('banner_section_enabled', true),
            'banner_section_title' => Setting::get('banner_section_title', 'Featured Promotions'),
            'banner_section_subtitle' => Setting::get('banner_section_subtitle', 'Discover amazing deals and offers from our partners'),
            'banner_auto_hide_when_empty' => Setting::get('banner_auto_hide_when_empty', true),
            'banner_max_display' => Setting::get('banner_max_display', 5),
        ];

        return view('admin.settings.banners', compact('settings'));
    }

    /**
     * Update banner settings.
     */
    public function updateBanners(Request $request): RedirectResponse
    {
        $request->validate([
            'banner_section_enabled' => 'boolean',
            'banner_section_title' => 'required|string|max:255',
            'banner_section_subtitle' => 'required|string|max:500',
            'banner_auto_hide_when_empty' => 'boolean',
            'banner_max_display' => 'required|integer|min:1|max:20',
        ]);

        // Update settings
        Setting::set('banner_section_enabled', $request->boolean('banner_section_enabled'), 'boolean', 'banners');
        Setting::set('banner_section_title', $request->banner_section_title, 'string', 'banners');
        Setting::set('banner_section_subtitle', $request->banner_section_subtitle, 'string', 'banners');
        Setting::set('banner_auto_hide_when_empty', $request->boolean('banner_auto_hide_when_empty'), 'boolean', 'banners');
        Setting::set('banner_max_display', $request->banner_max_display, 'integer', 'banners');

        // Clear homepage cache
        Cache::forget('homepage_data');
        Cache::flush(); // Clear all cache for now

        return back()->with('success', 'Banner settings updated successfully!');
    }

    /**
     * Show general settings.
     */
    public function general(): View
    {
        $settings = [
            'site_name' => Setting::get('site_name', 'BlogHub'),
            'site_description' => Setting::get('site_description', 'Your trusted source for deals and lifestyle content'),
            'site_keywords' => Setting::get('site_keywords', 'deals, coupons, lifestyle, blog'),
            'contact_email' => Setting::get('contact_email', '<EMAIL>'),
            'social_facebook' => Setting::get('social_facebook', ''),
            'social_twitter' => Setting::get('social_twitter', ''),
            'social_instagram' => Setting::get('social_instagram', ''),
            'social_linkedin' => Setting::get('social_linkedin', ''),
        ];

        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request): RedirectResponse
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'site_keywords' => 'required|string|max:500',
            'contact_email' => 'required|email|max:255',
            'social_facebook' => 'nullable|url|max:255',
            'social_twitter' => 'nullable|url|max:255',
            'social_instagram' => 'nullable|url|max:255',
            'social_linkedin' => 'nullable|url|max:255',
        ]);

        // Update settings
        Setting::set('site_name', $request->site_name, 'string', 'general');
        Setting::set('site_description', $request->site_description, 'string', 'general');
        Setting::set('site_keywords', $request->site_keywords, 'string', 'general');
        Setting::set('contact_email', $request->contact_email, 'string', 'general');
        Setting::set('social_facebook', $request->social_facebook, 'string', 'social');
        Setting::set('social_twitter', $request->social_twitter, 'string', 'social');
        Setting::set('social_instagram', $request->social_instagram, 'string', 'social');
        Setting::set('social_linkedin', $request->social_linkedin, 'string', 'social');

        // Clear cache
        Cache::flush();

        return back()->with('success', 'General settings updated successfully!');
    }

    /**
     * Show hero settings.
     */
    public function heroes(): View
    {
        $settings = [
            'hero_section_enabled' => Setting::get('hero_section_enabled', true),
            'hero_auto_hide_when_empty' => Setting::get('hero_auto_hide_when_empty', true),
            'hero_max_display' => Setting::get('hero_max_display', 1),
            'hero_transition_speed' => Setting::get('hero_transition_speed', 5000),
        ];

        return view('admin.settings.heroes', compact('settings'));
    }

    /**
     * Update hero settings.
     */
    public function updateHeroes(Request $request): RedirectResponse
    {
        $request->validate([
            'hero_section_enabled' => 'boolean',
            'hero_auto_hide_when_empty' => 'boolean',
            'hero_max_display' => 'required|integer|min:1|max:5',
            'hero_transition_speed' => 'required|integer|min:2000|max:15000',
        ]);

        // Update settings
        Setting::set('hero_section_enabled', $request->boolean('hero_section_enabled'), 'boolean', 'heroes');
        Setting::set('hero_auto_hide_when_empty', $request->boolean('hero_auto_hide_when_empty'), 'boolean', 'heroes');
        Setting::set('hero_max_display', $request->hero_max_display, 'integer', 'heroes');
        Setting::set('hero_transition_speed', $request->hero_transition_speed, 'integer', 'heroes');

        // Clear homepage cache
        Cache::forget('homepage_data');
        Cache::flush(); // Clear all cache for now

        return back()->with('success', 'Hero settings updated successfully!');
    }
}
