<nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="{{ route('home') }}" class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold gradient-text">BlogHub</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-8">
                    <a href="{{ route('home') }}"
                       class="nav-link {{ request()->routeIs('home') ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-blue-600' }} px-3 py-2 text-sm font-medium transition-colors duration-200">
                        Home
                    </a>
                    <a href="{{ route('blog.index') }}"
                       class="nav-link {{ request()->routeIs('blog.*') ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-blue-600' }} px-3 py-2 text-sm font-medium transition-colors duration-200">
                        Blog
                    </a>

                    <!-- Dynamic Blog Categories -->
                    @php
                        $blogCategories = \App\Models\Category::active()
                            ->whereHas('blogs', function($query) {
                                $query->published();
                            })
                            ->withCount('blogs')
                            ->ordered()
                            ->limit(4)
                            ->get();
                    @endphp

                    @foreach($blogCategories as $category)
                        <a href="{{ route('blog.index', ['category' => $category->slug]) }}"
                           class="nav-link {{ request('category') === $category->slug ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-blue-600' }} px-3 py-2 text-sm font-medium transition-colors duration-200">
                            {{ $category->name }}
                        </a>
                    @endforeach

                    <a href="{{ route('about') }}"
                       class="nav-link {{ request()->routeIs('about') ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-blue-600' }} px-3 py-2 text-sm font-medium transition-colors duration-200">
                        About
                    </a>
                    <a href="{{ route('contact') }}"
                       class="nav-link {{ request()->routeIs('contact') ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-blue-600' }} px-3 py-2 text-sm font-medium transition-colors duration-200">
                        Contact
                    </a>

                    <!-- Promotions link (hidden but accessible) -->
                    <a href="{{ route('promotions') }}"
                       class="nav-link text-gray-400 hover:text-gray-600 px-3 py-2 text-sm font-medium transition-colors duration-200 opacity-50">
                        Deals
                    </a>
                </div>
            </div>



            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button type="button"
                        class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                        aria-controls="mobile-menu"
                        aria-expanded="false">
                    <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div class="mobile-menu hidden md:hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
            <a href="{{ route('home') }}"
               class="block px-3 py-2 text-base font-medium {{ request()->routeIs('home') ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50' }}">
                Home
            </a>
            <a href="{{ route('blog.index') }}"
               class="block px-3 py-2 text-base font-medium {{ request()->routeIs('blog.*') ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50' }}">
                Blog
            </a>

            <!-- Mobile Blog Categories -->
            @foreach($blogCategories ?? [] as $category)
                <a href="{{ route('blog.index', ['category' => $category->slug]) }}"
                   class="block px-3 py-2 text-base font-medium {{ request('category') === $category->slug ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50' }}">
                    {{ $category->name }}
                </a>
            @endforeach

            <a href="{{ route('about') }}"
               class="block px-3 py-2 text-base font-medium {{ request()->routeIs('about') ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50' }}">
                About
            </a>
            <a href="{{ route('contact') }}"
               class="block px-3 py-2 text-base font-medium {{ request()->routeIs('contact') ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50' }}">
                Contact
            </a>


        </div>
    </div>
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});
</script>
