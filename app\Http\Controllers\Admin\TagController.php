<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;

class TagController extends Controller
{
    /**
     * Display a listing of tags.
     */
    public function index(): View
    {
        $tags = Tag::withCount(['blogs' => function ($query) {
            $query->published();
        }])->ordered()->paginate(15);
        
        return view('admin.tags.index', compact('tags'));
    }

    /**
     * Show the form for creating a new tag.
     */
    public function create(): View
    {
        return view('admin.tags.create');
    }

    /**
     * Store a newly created tag.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:tags,name',
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        Tag::create($request->all());

        // Clear cache
        $this->clearTagCaches();

        return redirect()->route('admin.tags.index')
                        ->with('success', 'Tag created successfully!');
    }

    /**
     * Display the specified tag.
     */
    public function show(Tag $tag): View
    {
        $tag->load(['blogs' => function ($query) {
            $query->published()->with(['primaryCategory', 'author'])->latest('published_at');
        }]);
        
        return view('admin.tags.show', compact('tag'));
    }

    /**
     * Show the form for editing the specified tag.
     */
    public function edit(Tag $tag): View
    {
        return view('admin.tags.edit', compact('tag'));
    }

    /**
     * Update the specified tag.
     */
    public function update(Request $request, Tag $tag): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:tags,name,' . $tag->id,
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $tag->update($request->all());

        // Clear cache
        $this->clearTagCaches();

        return redirect()->route('admin.tags.index')
                        ->with('success', 'Tag updated successfully!');
    }

    /**
     * Remove the specified tag.
     */
    public function destroy(Tag $tag): RedirectResponse
    {
        $tag->delete();

        // Clear cache
        $this->clearTagCaches();

        return redirect()->route('admin.tags.index')
                        ->with('success', 'Tag deleted successfully!');
    }

    /**
     * Toggle tag active status.
     */
    public function toggleActive(Tag $tag): RedirectResponse
    {
        $tag->update(['is_active' => !$tag->is_active]);
        
        // Clear cache
        $this->clearTagCaches();

        $status = $tag->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Tag {$status} successfully!");
    }

    /**
     * Clear tag-related caches.
     */
    private function clearTagCaches(): void
    {
        Cache::forget('popular_tags');
        Cache::forget('blog_tags');
        Cache::flush(); // Clear all cache for now
    }
}
