<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('code')->nullable(); // Coupon code (null for deals without codes)
            $table->enum('type', ['coupon', 'deal', 'cashback', 'freebie']);
            $table->enum('discount_type', ['percentage', 'fixed', 'free_shipping', 'bogo'])->nullable();
            $table->decimal('discount_value', 10, 2)->nullable(); // Amount or percentage
            $table->string('currency', 3)->default('USD');
            $table->decimal('minimum_order', 10, 2)->nullable();
            $table->string('affiliate_url');
            $table->unsignedBigInteger('store_id');
            $table->unsignedBigInteger('category_id')->nullable();
            $table->datetime('starts_at')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->boolean('is_exclusive')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('usage_count')->default(0);
            $table->integer('success_count')->default(0);
            $table->decimal('success_rate', 5, 2)->default(0); // Calculated field
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('meta_keywords')->nullable();
            $table->text('terms_conditions')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->foreign('store_id')->references('id')->on('stores')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
            
            $table->index(['is_active', 'expires_at']);
            $table->index(['store_id', 'is_active']);
            $table->index(['category_id']);
            $table->index(['type', 'is_active']);
            $table->index(['is_featured', 'is_active']);
            $table->index(['expires_at']);
            $table->fullText(['title', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
