<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create admin user
        // User::create([
        //     'name' => 'Admin User',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('password123'),
        //     'email_verified_at' => now(),
        // ]);

        // // Create demo users
        // User::create([
        //     'name' => '<PERSON>',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('password123'),
        //     'email_verified_at' => now(),
        // ]);

        // User::create([
        //     'name' => '<PERSON>',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('password123'),
        //     'email_verified_at' => now(),
        // ]);
    }
}
