<?php $__env->startSection('title', 'Hero Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Hero Settings</h1>
            <p class="text-gray-600">Configure hero section display and behavior</p>
        </div>
    </div>

    <!-- Settings Form -->
    <form action="<?php echo e(route('admin.settings.heroes.update')); ?>" method="POST" class="space-y-6">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Section Control -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Section Control</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="hero_section_enabled" 
                                   name="hero_section_enabled" 
                                   value="1" 
                                   class="form-checkbox" 
                                   <?php echo e($settings['hero_section_enabled'] ? 'checked' : ''); ?>>
                            <label for="hero_section_enabled" class="ml-2 text-sm text-gray-700">
                                Enable Hero Section
                            </label>
                        </div>
                        <p class="text-sm text-gray-500">When disabled, the entire hero section will be hidden from the homepage.</p>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="hero_auto_hide_when_empty" 
                                   name="hero_auto_hide_when_empty" 
                                   value="1" 
                                   class="form-checkbox" 
                                   <?php echo e($settings['hero_auto_hide_when_empty'] ? 'checked' : ''); ?>>
                            <label for="hero_auto_hide_when_empty" class="ml-2 text-sm text-gray-700">
                                Auto-hide When Empty
                            </label>
                        </div>
                        <p class="text-sm text-gray-500">Automatically hide the hero section when no active heroes are available.</p>

                        <div>
                            <label for="hero_max_display" class="form-label">Maximum Heroes to Display</label>
                            <input type="number" 
                                   id="hero_max_display" 
                                   name="hero_max_display" 
                                   class="form-input <?php $__errorArgs = ['hero_max_display'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('hero_max_display', $settings['hero_max_display'])); ?>" 
                                   min="1" 
                                   max="5" 
                                   required>
                            <p class="text-sm text-gray-500 mt-1">Maximum number of heroes to show on homepage (1-5, usually 1)</p>
                            <?php $__errorArgs = ['hero_max_display'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="hero_transition_speed" class="form-label">Auto-Slide Duration (ms)</label>
                            <input type="number"
                                   id="hero_transition_speed"
                                   name="hero_transition_speed"
                                   class="form-input <?php $__errorArgs = ['hero_transition_speed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   value="<?php echo e(old('hero_transition_speed', $settings['hero_transition_speed'])); ?>"
                                   min="2000"
                                   max="15000"
                                   step="500"
                                   required>
                            <p class="text-sm text-gray-500 mt-1">How long each hero slide is displayed before automatically transitioning to the next (2000-15000ms). Only applies when multiple heroes are active.</p>
                            <?php $__errorArgs = ['hero_transition_speed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Preview -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Hero Section Preview</h3>
                    </div>
                    <div class="card-body">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-8 rounded-lg text-white text-center">
                            <h2 class="text-3xl font-bold mb-4">Hero Section</h2>
                            <p class="text-lg mb-6">This is how your hero section will appear on the homepage</p>
                            <div class="flex justify-center space-x-4">
                                <div class="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium">
                                    Primary Button
                                </div>
                                <div class="border-2 border-white px-6 py-2 rounded-lg font-medium">
                                    Secondary Button
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>Auto-Slide Duration:</span>
                                <span id="speed-display"><?php echo e($settings['hero_transition_speed']); ?>ms</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Max Heroes:</span>
                                <span id="max-display"><?php echo e($settings['hero_max_display']); ?></span>
                            </div>
                            <div class="text-xs text-gray-500 mt-2">
                                Auto-slide only works when multiple heroes are active
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Heroes -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Current Active Heroes</h3>
                    </div>
                    <div class="card-body">
                        <?php
                            $activeHeroes = \App\Models\Hero::active()->ordered()->limit(5)->get();
                        ?>
                        
                        <?php if($activeHeroes->count() > 0): ?>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $activeHeroes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hero): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                                        <div class="w-8 h-8 rounded flex items-center justify-center text-white text-xs font-medium"
                                             style="background: <?php echo e($hero->background_color); ?>">
                                            <?php echo e($hero->sort_order); ?>

                                        </div>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium"><?php echo e($hero->title); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo e(ucfirst($hero->text_position)); ?> • <?php echo e(ucfirst($hero->text_color)); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 text-center py-4">No active heroes found</p>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <a href="<?php echo e(route('admin.heroes.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                                Manage Heroes →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="<?php echo e(route('admin.heroes.index')); ?>" class="btn-secondary">Back to Heroes</a>
            <button type="submit" class="btn-primary">Save Settings</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const speedInput = document.getElementById('hero_transition_speed');
    const maxDisplayInput = document.getElementById('hero_max_display');
    const speedDisplay = document.getElementById('speed-display');
    const maxDisplay = document.getElementById('max-display');

    speedInput.addEventListener('input', function() {
        speedDisplay.textContent = this.value + 'ms';
    });

    maxDisplayInput.addEventListener('input', function() {
        maxDisplay.textContent = this.value;
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/admin/settings/heroes.blade.php ENDPATH**/ ?>