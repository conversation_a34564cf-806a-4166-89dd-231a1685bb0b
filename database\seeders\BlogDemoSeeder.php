<?php

namespace Database\Seeders;

use App\Models\Blog;
use App\Models\Category;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BlogDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create demo categories with images
        $categories = [
            [
                'name' => 'Technology',
                'description' => 'Latest tech trends, gadgets, and innovations',
                'image' => 'categories/technology.jpg',
                'color' => '#3B82F6',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Lifestyle',
                'description' => 'Tips for better living, wellness, and personal growth',
                'image' => 'categories/lifestyle.jpg',
                'color' => '#10B981',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Health & Fitness',
                'description' => 'Health tips, fitness routines, and wellness advice',
                'image' => 'categories/health.jpg',
                'color' => '#EF4444',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Travel',
                'description' => 'Travel guides, destinations, and adventure stories',
                'image' => 'categories/travel.jpg',
                'color' => '#F59E0B',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Food & Cooking',
                'description' => 'Recipes, cooking tips, and culinary adventures',
                'image' => 'categories/food.jpg',
                'color' => '#8B5CF6',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Business',
                'description' => 'Business insights, entrepreneurship, and career advice',
                'image' => 'categories/business.jpg',
                'color' => '#06B6D4',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Education',
                'description' => 'Learning resources, study tips, and educational content',
                'image' => 'categories/education.jpg',
                'color' => '#84CC16',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Entertainment',
                'description' => 'Movies, music, games, and pop culture',
                'image' => 'categories/entertainment.jpg',
                'color' => '#EC4899',
                'is_active' => true,
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $categoryData) {
            $categoryData['slug'] = Str::slug($categoryData['name']);
            Category::create($categoryData);
        }

        // Create demo tags
        $tags = [
            ['name' => 'AI & Machine Learning', 'color' => '#3B82F6'],
            ['name' => 'Web Development', 'color' => '#10B981'],
            ['name' => 'Mobile Apps', 'color' => '#F59E0B'],
            ['name' => 'Productivity', 'color' => '#8B5CF6'],
            ['name' => 'Mindfulness', 'color' => '#06B6D4'],
            ['name' => 'Nutrition', 'color' => '#84CC16'],
            ['name' => 'Fitness', 'color' => '#EF4444'],
            ['name' => 'Adventure', 'color' => '#F97316'],
            ['name' => 'Photography', 'color' => '#EC4899'],
            ['name' => 'Recipes', 'color' => '#8B5CF6'],
            ['name' => 'Startup', 'color' => '#06B6D4'],
            ['name' => 'Leadership', 'color' => '#84CC16'],
            ['name' => 'Online Learning', 'color' => '#3B82F6'],
            ['name' => 'Movies', 'color' => '#EC4899'],
            ['name' => 'Gaming', 'color' => '#8B5CF6'],
        ];

        foreach ($tags as $tagData) {
            $tagData['slug'] = Str::slug($tagData['name']);
            $tagData['is_active'] = true;
            Tag::create($tagData);
        }

        // Get or create a demo user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Demo Author',
                'password' => bcrypt('password'),
            ]
        );

        // Create demo blogs
        $blogTitles = [
            'The Future of Artificial Intelligence in Everyday Life',
            '10 Productivity Hacks That Will Transform Your Day',
            'Mindful Living: A Complete Guide to Inner Peace',
            'The Ultimate Guide to Healthy Meal Prep',
            'Exploring Hidden Gems: Off-the-Beaten-Path Destinations',
            'Building Your First Mobile App: A Beginner\'s Journey',
            'The Science of Happiness: What Research Tells Us',
            'Sustainable Living: Small Changes, Big Impact',
            'Mastering Remote Work: Tips from Digital Nomads',
            'The Art of Photography: Capturing Perfect Moments',
            'Cryptocurrency Explained: A Beginner\'s Guide',
            'Home Workout Routines That Actually Work',
            'The Psychology of Success: Mindset Matters',
            'Cooking Around the World: International Flavors at Home',
            'Digital Detox: Reclaiming Your Time and Attention',
            'The Rise of E-commerce: Trends and Opportunities',
            'Learning Languages: Effective Strategies for Adults',
            'Movie Reviews: Hidden Gems You Shouldn\'t Miss',
            'Gaming Culture: How Video Games Shape Society',
            'Financial Planning for Millennials: A Practical Guide',
        ];

        $categories = Category::all();
        $tags = Tag::all();

        foreach ($blogTitles as $index => $title) {
            $category = $categories->random();
            $selectedTags = $tags->random(rand(2, 4));
            
            $blog = Blog::create([
                'title' => $title,
                'slug' => Str::slug($title),
                'excerpt' => 'This is an engaging excerpt that provides a preview of the article content and entices readers to continue reading.',
                'content' => $this->generateBlogContent($title),
                'category_id' => $category->id,
                'author_id' => $user->id,
                'is_published' => true,
                'is_featured' => $index < 6, // First 6 blogs are featured
                'published_at' => now()->subDays(rand(1, 30)),
                'views_count' => rand(100, 5000),
            ]);

            // Attach tags
            $blog->tags()->attach($selectedTags->pluck('id'));
            
            // Attach to additional categories (many-to-many)
            $additionalCategories = $categories->except($category->id)->random(rand(0, 2));
            $blog->categories()->attach($additionalCategories->pluck('id'));
        }
    }

    private function generateBlogContent($title): string
    {
        return "
        <h2>Introduction</h2>
        <p>Welcome to this comprehensive guide about {$title}. In this article, we'll explore the key concepts, practical applications, and actionable insights that will help you understand this topic better.</p>
        
        <h2>Key Points</h2>
        <p>Here are the main points we'll cover in this detailed exploration:</p>
        <ul>
            <li>Understanding the fundamentals and core concepts</li>
            <li>Practical applications and real-world examples</li>
            <li>Best practices and expert recommendations</li>
            <li>Common challenges and how to overcome them</li>
            <li>Future trends and developments to watch</li>
        </ul>
        
        <h2>Detailed Analysis</h2>
        <p>Let's dive deeper into each aspect of this topic. Through careful analysis and research, we've compiled the most relevant and up-to-date information to provide you with valuable insights.</p>
        
        <blockquote>
            <p>\"Knowledge is power, but knowledge shared is power multiplied.\" - This principle guides our approach to creating content that truly adds value to our readers' lives.</p>
        </blockquote>
        
        <h2>Practical Tips</h2>
        <p>Here are some actionable tips you can implement right away:</p>
        <ol>
            <li>Start with small, manageable steps</li>
            <li>Stay consistent with your approach</li>
            <li>Monitor your progress regularly</li>
            <li>Adjust your strategy based on results</li>
            <li>Share your experiences with others</li>
        </ol>
        
        <h2>Conclusion</h2>
        <p>In conclusion, understanding {$title} is crucial for anyone looking to stay informed and make better decisions. We hope this guide has provided you with valuable insights and practical knowledge that you can apply in your daily life.</p>
        
        <p>Remember to bookmark this article for future reference and share it with others who might find it helpful. Stay tuned for more informative content!</p>
        ";
    }
}
