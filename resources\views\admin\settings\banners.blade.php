@extends('layouts.admin')

@section('title', 'Banner Settings')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Banner Settings</h1>
            <p class="text-gray-600">Configure banner section display and behavior</p>
        </div>
    </div>

    <!-- Settings Form -->
    <form action="{{ route('admin.settings.banners.update') }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Section Control -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Section Control</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="banner_section_enabled" 
                                   name="banner_section_enabled" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ $settings['banner_section_enabled'] ? 'checked' : '' }}>
                            <label for="banner_section_enabled" class="ml-2 text-sm text-gray-700">
                                Enable Banner Section
                            </label>
                        </div>
                        <p class="text-sm text-gray-500">When disabled, the entire banner section will be hidden from the homepage.</p>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="banner_auto_hide_when_empty" 
                                   name="banner_auto_hide_when_empty" 
                                   value="1" 
                                   class="form-checkbox" 
                                   {{ $settings['banner_auto_hide_when_empty'] ? 'checked' : '' }}>
                            <label for="banner_auto_hide_when_empty" class="ml-2 text-sm text-gray-700">
                                Auto-hide When Empty
                            </label>
                        </div>
                        <p class="text-sm text-gray-500">Automatically hide the banner section when no active banners are available.</p>

                        <div>
                            <label for="banner_max_display" class="form-label">Maximum Banners to Display</label>
                            <input type="number" 
                                   id="banner_max_display" 
                                   name="banner_max_display" 
                                   class="form-input @error('banner_max_display') border-red-500 @enderror" 
                                   value="{{ old('banner_max_display', $settings['banner_max_display']) }}" 
                                   min="1" 
                                   max="20" 
                                   required>
                            <p class="text-sm text-gray-500 mt-1">Maximum number of banners to show on homepage (1-20)</p>
                            @error('banner_max_display')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Section Content -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Section Content</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="banner_section_title" class="form-label">Section Title *</label>
                            <input type="text" 
                                   id="banner_section_title" 
                                   name="banner_section_title" 
                                   class="form-input @error('banner_section_title') border-red-500 @enderror" 
                                   value="{{ old('banner_section_title', $settings['banner_section_title']) }}" 
                                   required>
                            @error('banner_section_title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="banner_section_subtitle" class="form-label">Section Subtitle *</label>
                            <textarea id="banner_section_subtitle" 
                                      name="banner_section_subtitle" 
                                      rows="3" 
                                      class="form-input @error('banner_section_subtitle') border-red-500 @enderror"
                                      required>{{ old('banner_section_subtitle', $settings['banner_section_subtitle']) }}</textarea>
                            @error('banner_section_subtitle')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Preview</h3>
                    </div>
                    <div class="card-body">
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <h3 class="text-2xl font-bold text-gray-900 mb-2" id="preview-title">
                                {{ $settings['banner_section_title'] }}
                            </h3>
                            <p class="text-gray-600" id="preview-subtitle">
                                {{ $settings['banner_section_subtitle'] }}
                            </p>
                            <div class="mt-4 bg-gray-200 h-32 rounded-lg flex items-center justify-center">
                                <span class="text-gray-500">Banner Slider Area</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.banners.index') }}" class="btn-secondary">Back to Banners</a>
            <button type="submit" class="btn-primary">Save Settings</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('banner_section_title');
    const subtitleInput = document.getElementById('banner_section_subtitle');
    const previewTitle = document.getElementById('preview-title');
    const previewSubtitle = document.getElementById('preview-subtitle');

    titleInput.addEventListener('input', function() {
        previewTitle.textContent = this.value || 'Section Title';
    });

    subtitleInput.addEventListener('input', function() {
        previewSubtitle.textContent = this.value || 'Section subtitle';
    });
});
</script>
@endsection
