@extends('layouts.admin')

@section('title', 'Trashed Banners')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Trashed Banners</h1>
            <p class="text-gray-600">Manage deleted banners - restore or permanently delete</p>
        </div>
        <a href="{{ route('admin.banners.index') }}" class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Back to Banners
        </a>
    </div>

    <!-- Filters -->
    <div class="card">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.banners.trash') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Date From -->
                    <div>
                        <label for="date_from" class="form-label">Deleted From</label>
                        <input type="date" name="date_from" id="date_from" class="form-input" value="{{ request('date_from') }}">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date_to" class="form-label">Deleted To</label>
                        <input type="date" name="date_to" id="date_to" class="form-input" value="{{ request('date_to') }}">
                    </div>

                    <!-- Sort By -->
                    <div>
                        <label for="sort_by" class="form-label">Sort By</label>
                        <select name="sort_by" id="sort_by" class="form-input">
                            <option value="deleted_at" {{ request('sort_by', 'deleted_at') === 'deleted_at' ? 'selected' : '' }}>Deleted Date</option>
                            <option value="title" {{ request('sort_by') === 'title' ? 'selected' : '' }}>Title</option>
                            <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>Created Date</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <button type="submit" class="btn-primary">Apply Filters</button>
                        <a href="{{ route('admin.banners.trash') }}" class="btn-secondary">Clear</a>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <label for="sort_direction" class="text-sm text-gray-600">Direction:</label>
                        <select name="sort_direction" id="sort_direction" class="form-input w-auto">
                            <option value="desc" {{ request('sort_direction', 'desc') === 'desc' ? 'selected' : '' }}>Descending</option>
                            <option value="asc" {{ request('sort_direction') === 'asc' ? 'selected' : '' }}>Ascending</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Trashed Banners Table -->
    <div class="card">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banner</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Link</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($trashedBanners as $banner)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-16 w-24">
                                        <img class="h-16 w-24 rounded-lg object-cover opacity-60" 
                                             src="{{ $banner->image_url }}" 
                                             alt="{{ $banner->title }}">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $banner->title }}</div>
                                        @if($banner->description)
                                            <div class="text-sm text-gray-500">{{ Str::limit($banner->description, 50) }}</div>
                                        @endif
                                        <div class="text-xs text-gray-400">Created: {{ $banner->created_at->format('M j, Y') }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                @if($banner->link_url)
                                    <div class="space-y-1">
                                        <div class="text-blue-600 truncate max-w-xs">{{ $banner->link_url }}</div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs bg-gray-100 px-2 py-1 rounded">{{ $banner->link_text }}</span>
                                            <span class="text-xs {{ $banner->link_target === '_blank' ? 'text-blue-600' : 'text-gray-500' }}">
                                                {{ $banner->link_target === '_blank' ? 'New Tab' : 'Same Tab' }}
                                            </span>
                                        </div>
                                    </div>
                                @else
                                    <span class="text-gray-400">No link</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div>{{ $banner->deleted_at->format('M j, Y') }}</div>
                                <div class="text-xs text-gray-400">{{ $banner->deleted_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <form method="POST" action="{{ route('admin.banners.restore', $banner->id) }}" class="inline">
                                    @csrf
                                    <button type="submit" 
                                            class="text-green-600 hover:text-green-900"
                                            onclick="return confirm('Are you sure you want to restore this banner?')">
                                        Restore
                                    </button>
                                </form>
                                
                                <form method="POST" action="{{ route('admin.banners.force-delete', $banner->id) }}" 
                                      class="inline" 
                                      onsubmit="return confirm('Are you sure you want to permanently delete this banner? This action cannot be undone!')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete Forever</button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="px-6 py-12 text-center text-gray-500">
                                <div class="space-y-2">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                    <p>No trashed banners found</p>
                                    <a href="{{ route('admin.banners.index') }}" class="text-blue-600 hover:text-blue-800">Go back to banners</a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if($trashedBanners->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $trashedBanners->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
