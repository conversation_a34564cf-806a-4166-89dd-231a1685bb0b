@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Store Header -->
    <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Breadcrumbs -->
            <nav class="mb-6">
                <ol class="flex items-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700">Home</a></li>
                    <li><span class="text-gray-400">/</span></li>
                    <li><a href="{{ route('stores.index') }}" class="text-gray-500 hover:text-gray-700">Stores</a></li>
                    <li><span class="text-gray-400">/</span></li>
                    <li class="text-gray-900 font-medium">{{ $store->name ?? 'Sample Store' }}</li>
                </ol>
            </nav>

            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-center space-x-6 mb-6 lg:mb-0">
                    <!-- Store Logo -->
                    @if($store->logo ?? false)
                        <img src="{{ asset('storage/' . $store->logo) }}" 
                             alt="{{ $store->name }}" 
                             class="w-20 h-20 lg:w-24 lg:h-24 rounded-xl object-cover">
                    @else
                        <div class="w-20 h-20 lg:w-24 lg:h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <span class="text-white text-3xl lg:text-4xl font-bold">{{ substr($store->name ?? 'S', 0, 1) }}</span>
                        </div>
                    @endif

                    <div>
                        <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                            {{ $store->name ?? 'Sample Store' }}
                        </h1>
                        @if($store->description ?? false)
                            <p class="text-lg text-gray-600 max-w-2xl">{{ Str::limit($store->description, 120) }}</p>
                        @else
                            <p class="text-lg text-gray-600 max-w-2xl">Discover amazing deals and exclusive offers from this popular retailer.</p>
                        @endif
                        
                        <!-- Store Rating -->
                        @if($store->rating ?? false)
                            <div class="flex items-center mt-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= $store->rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                                             fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-gray-600">{{ number_format($store->rating, 1) }} out of 5</span>
                            </div>
                        @else
                            <div class="flex items-center mt-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= 4.5 ? 'text-yellow-400' : 'text-gray-300' }}" 
                                             fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-gray-600">4.5 out of 5</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Store Actions -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('stores.visit', $store->slug ?? 'sample') }}" 
                       target="_blank"
                       class="btn-primary px-6 py-3"
                       onclick="trackStoreVisit('{{ $store->slug ?? 'sample' }}')">
                        Visit Store
                    </a>
                    <button class="btn-outline px-6 py-3" onclick="toggleFavorite()">
                        Add to Favorites
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Store Stats -->
    <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $stats['active_coupons'] ?? 25 }}</div>
                    <div class="text-sm text-gray-600">Active Deals</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ number_format($stats['avg_success_rate'] ?? 87, 0) }}%</div>
                    <div class="text-sm text-gray-600">Success Rate</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ number_format($stats['total_clicks'] ?? 15420) }}</div>
                    <div class="text-sm text-gray-600">Total Clicks</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ $store->created_at->diffForHumans() ?? '2 years ago' }}</div>
                    <div class="text-sm text-gray-600">Added</div>
                </div>
            </div>
        </div>
    </section>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1">
                <div class="card sticky top-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Filter Deals</h3>
                        
                        <!-- Category Filter -->
                        @if(isset($storeCategories) && $storeCategories->count() > 0)
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Categories</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="" checked class="text-blue-600">
                                    <span class="ml-2 text-sm text-gray-700">All Categories</span>
                                </label>
                                @foreach($storeCategories as $category)
                                    <label class="flex items-center">
                                        <input type="radio" name="category" value="{{ $category->slug }}" class="text-blue-600">
                                        <span class="ml-2 text-sm text-gray-700">{{ $category->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Deal Type Filter -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Deal Type</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="" checked class="text-blue-600">
                                    <span class="ml-2 text-sm text-gray-700">All Types</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="coupon" class="text-blue-600">
                                    <span class="ml-2 text-sm text-gray-700">Coupon Codes</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="deal" class="text-blue-600">
                                    <span class="ml-2 text-sm text-gray-700">Deals</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="cashback" class="text-blue-600">
                                    <span class="ml-2 text-sm text-gray-700">Cashback</span>
                                </label>
                            </div>
                        </div>

                        <!-- Sort Options -->
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">Sort By</h4>
                            <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option value="newest">Newest First</option>
                                <option value="popular">Most Popular</option>
                                <option value="expiring">Expiring Soon</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Deals Grid -->
            <div class="lg:col-span-3">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">
                        Available Deals ({{ $coupons->total() ?? 25 }})
                    </h2>
                </div>

                <div id="coupons-grid" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @forelse($coupons ?? [] as $coupon)
                        @include('components.coupon-card', ['coupon' => $coupon])
                    @empty
                        <!-- Demo Coupons -->
                        @for($i = 0; $i < 6; $i++)
                            @php
                                $demoCoupons = [
                                    ['title' => '50% Off Everything', 'code' => 'SAVE50', 'type' => 'coupon', 'discount' => '50% OFF'],
                                    ['title' => 'Free Shipping on Orders $50+', 'code' => 'FREESHIP', 'type' => 'deal', 'discount' => 'FREE SHIPPING'],
                                    ['title' => 'Buy 2 Get 1 Free', 'code' => 'BOGO', 'type' => 'deal', 'discount' => 'BOGO'],
                                    ['title' => '$20 Off Orders Over $100', 'code' => 'SAVE20', 'type' => 'coupon', 'discount' => '$20 OFF'],
                                    ['title' => '30% Off New Arrivals', 'code' => 'NEW30', 'type' => 'coupon', 'discount' => '30% OFF'],
                                    ['title' => 'Flash Sale - 60% Off', 'code' => 'FLASH60', 'type' => 'deal', 'discount' => '60% OFF'],
                                ];
                                $coupon = $demoCoupons[$i % count($demoCoupons)];
                            @endphp
                            
                            <div class="card-hover">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <span class="badge-{{ $coupon['type'] === 'coupon' ? 'danger' : 'success' }}">
                                            {{ $coupon['discount'] }}
                                        </span>
                                        <span class="text-xs text-gray-500">Expires: Dec 31</span>
                                    </div>
                                    <h3 class="font-semibold text-lg mb-2">{{ $coupon['title'] }}</h3>
                                    <p class="text-gray-600 text-sm mb-4">Get amazing savings with this exclusive offer. Limited time only!</p>
                                    
                                    @if($coupon['type'] === 'coupon')
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg mb-4">
                                            <span class="text-sm font-medium">Code:</span>
                                            <div class="flex items-center space-x-2">
                                                <code class="bg-white px-2 py-1 rounded text-sm font-mono">{{ $coupon['code'] }}</code>
                                                <button class="text-blue-600 text-sm">Copy</button>
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <div class="flex space-x-2">
                                        <button class="flex-1 btn-primary text-sm">
                                            {{ $coupon['type'] === 'coupon' ? 'Get Code' : 'Get Deal' }}
                                        </button>
                                        <button class="btn-outline text-sm px-4">Details</button>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    @endforelse
                </div>

                <!-- Pagination -->
                @if(isset($coupons) && $coupons->hasPages())
                    <div class="mt-8">
                        {{ $coupons->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Store Information -->
    @if($store->description ?? false)
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">About {{ $store->name }}</h2>
                <div class="prose prose-lg mx-auto">
                    <p>{{ $store->description }}</p>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Related Stores -->
    @if(isset($relatedStores) && $relatedStores->count() > 0)
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Similar Stores</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                @foreach($relatedStores as $relatedStore)
                    @include('components.store-card', ['store' => $relatedStore])
                @endforeach
            </div>
        </div>
    </section>
    @endif
</div>

<script>
function trackStoreVisit(slug) {
    fetch(`/stores/${slug}/visit`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    });
}

function toggleFavorite() {
    // This would integrate with a favorites system
    alert('Added to favorites! (This would integrate with user accounts)');
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const categoryInputs = document.querySelectorAll('input[name="category"]');
    const typeInputs = document.querySelectorAll('input[name="type"]');
    const sortSelect = document.querySelector('select[name="sort"]');
    
    function updateDeals() {
        const category = document.querySelector('input[name="category"]:checked').value;
        const type = document.querySelector('input[name="type"]:checked').value;
        const sort = sortSelect.value;
        
        // This would make an AJAX request to filter deals
        console.log('Filtering:', { category, type, sort });
    }
    
    categoryInputs.forEach(input => input.addEventListener('change', updateDeals));
    typeInputs.forEach(input => input.addEventListener('change', updateDeals));
    sortSelect.addEventListener('change', updateDeals);
});
</script>
@endsection
