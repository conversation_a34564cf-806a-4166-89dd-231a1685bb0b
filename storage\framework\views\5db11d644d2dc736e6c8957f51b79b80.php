<?php $__env->startSection('title', 'Edit Hero'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center space-x-4">
        <a href="<?php echo e(route('admin.heroes.index')); ?>" class="text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Hero</h1>
            <p class="text-gray-600">Update hero section details</p>
        </div>
    </div>

    <!-- Current Image Preview -->
    <?php if($hero->background_image): ?>
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium">Current Background Image</h3>
        </div>
        <div class="card-body">
            <div class="flex items-center space-x-4">
                <img src="<?php echo e($hero->background_image_url); ?>" alt="Current background" class="w-32 h-20 object-cover rounded-lg">
                <div>
                    <p class="text-sm text-gray-600">Current background image</p>
                    <p class="text-xs text-gray-500">Upload a new image to replace this one</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Form -->
    <form action="<?php echo e(route('admin.heroes.update', $hero)); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Left Column -->
            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   class="form-input <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('title', $hero->title)); ?>" 
                                   required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   class="form-input <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('subtitle', $hero->subtitle)); ?>" 
                                   placeholder="Optional subtitle">
                            <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="description" class="form-label">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="4" 
                                      class="form-input <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      placeholder="Optional description for the hero section"><?php echo e(old('description', $hero->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Background Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Background Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="background_image" class="form-label">Background Image</label>
                            <input type="file" 
                                   id="background_image" 
                                   name="background_image" 
                                   accept="image/*" 
                                   class="form-input <?php $__errorArgs = ['background_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <p class="text-sm text-gray-500 mt-1">Recommended size: 1920x1080px. Max size: 2MB. Leave empty to keep current image.</p>
                            <?php $__errorArgs = ['background_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="background_color" class="form-label">Background Color *</label>
                            <div class="flex items-center space-x-2">
                                <?php
                                    $bgColor = old('background_color', $hero->background_color);
                                    $isHexColor = preg_match('/^#[0-9A-Fa-f]{6}$/', $bgColor);
                                ?>
                                <input type="color" 
                                       id="background_color" 
                                       name="background_color_picker" 
                                       class="w-12 h-10 border border-gray-300 rounded cursor-pointer" 
                                       value="<?php echo e($isHexColor ? $bgColor : '#3B82F6'); ?>">
                                <input type="text" 
                                       id="background_color_text" 
                                       name="background_color"
                                       class="form-input flex-1 <?php $__errorArgs = ['background_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       value="<?php echo e($bgColor); ?>" 
                                       placeholder="#3B82F6 or linear-gradient(...)"
                                       required>
                            </div>
                            <p class="text-sm text-gray-500 mt-1">Use hex color or CSS gradient (e.g., linear-gradient(135deg, #667eea 0%, #764ba2 100%))</p>
                            <?php $__errorArgs = ['background_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Button Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Button Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <!-- Primary Button -->
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Primary Button</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="primary_button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           id="primary_button_text" 
                                           name="primary_button_text" 
                                           class="form-input <?php $__errorArgs = ['primary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           value="<?php echo e(old('primary_button_text', $hero->primary_button_text)); ?>" 
                                           placeholder="Get Started">
                                    <?php $__errorArgs = ['primary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div>
                                    <label for="primary_button_url" class="form-label">Button URL</label>
                                    <input type="url" 
                                           id="primary_button_url" 
                                           name="primary_button_url" 
                                           class="form-input <?php $__errorArgs = ['primary_button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           value="<?php echo e(old('primary_button_url', $hero->primary_button_url)); ?>" 
                                           placeholder="https://example.com">
                                    <?php $__errorArgs = ['primary_button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="primary_button_style" class="form-label">Button Style *</label>
                                <select id="primary_button_style" 
                                        name="primary_button_style" 
                                        class="form-input <?php $__errorArgs = ['primary_button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        required>
                                    <option value="primary" <?php echo e(old('primary_button_style', $hero->primary_button_style) === 'primary' ? 'selected' : ''); ?>>Primary (Gradient)</option>
                                    <option value="secondary" <?php echo e(old('primary_button_style', $hero->primary_button_style) === 'secondary' ? 'selected' : ''); ?>>Secondary (Solid)</option>
                                    <option value="outline" <?php echo e(old('primary_button_style', $hero->primary_button_style) === 'outline' ? 'selected' : ''); ?>>Outline</option>
                                </select>
                                <?php $__errorArgs = ['primary_button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Secondary Button -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">Secondary Button</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="secondary_button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           id="secondary_button_text" 
                                           name="secondary_button_text" 
                                           class="form-input <?php $__errorArgs = ['secondary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           value="<?php echo e(old('secondary_button_text', $hero->secondary_button_text)); ?>" 
                                           placeholder="Learn More">
                                    <?php $__errorArgs = ['secondary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div>
                                    <label for="secondary_button_url" class="form-label">Button URL</label>
                                    <input type="url" 
                                           id="secondary_button_url" 
                                           name="secondary_button_url" 
                                           class="form-input <?php $__errorArgs = ['secondary_button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           value="<?php echo e(old('secondary_button_url', $hero->secondary_button_url)); ?>" 
                                           placeholder="https://example.com">
                                    <?php $__errorArgs = ['secondary_button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="secondary_button_style" class="form-label">Button Style *</label>
                                <select id="secondary_button_style" 
                                        name="secondary_button_style" 
                                        class="form-input <?php $__errorArgs = ['secondary_button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        required>
                                    <option value="primary" <?php echo e(old('secondary_button_style', $hero->secondary_button_style) === 'primary' ? 'selected' : ''); ?>>Primary (Gradient)</option>
                                    <option value="secondary" <?php echo e(old('secondary_button_style', $hero->secondary_button_style) === 'secondary' ? 'selected' : ''); ?>>Secondary (Solid)</option>
                                    <option value="outline" <?php echo e(old('secondary_button_style', $hero->secondary_button_style) === 'outline' ? 'selected' : ''); ?>>Outline</option>
                                </select>
                                <?php $__errorArgs = ['secondary_button_style'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Display Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Display Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="text_position" class="form-label">Text Position *</label>
                            <select id="text_position" 
                                    name="text_position" 
                                    class="form-input <?php $__errorArgs = ['text_position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    required>
                                <option value="left" <?php echo e(old('text_position', $hero->text_position) === 'left' ? 'selected' : ''); ?>>Left</option>
                                <option value="center" <?php echo e(old('text_position', $hero->text_position) === 'center' ? 'selected' : ''); ?>>Center</option>
                                <option value="right" <?php echo e(old('text_position', $hero->text_position) === 'right' ? 'selected' : ''); ?>>Right</option>
                            </select>
                            <?php $__errorArgs = ['text_position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="text_color" class="form-label">Text Color *</label>
                            <select id="text_color" 
                                    name="text_color" 
                                    class="form-input <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    required>
                                <option value="white" <?php echo e(old('text_color', $hero->text_color) === 'white' ? 'selected' : ''); ?>>White</option>
                                <option value="dark" <?php echo e(old('text_color', $hero->text_color) === 'dark' ? 'selected' : ''); ?>>Dark</option>
                            </select>
                            <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   class="form-input <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('sort_order', $hero->sort_order)); ?>" 
                                   min="0">
                            <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                            <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Schedule Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Schedule Settings</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <div>
                            <label for="starts_at" class="form-label">Start Date</label>
                            <input type="datetime-local" 
                                   id="starts_at" 
                                   name="starts_at" 
                                   class="form-input <?php $__errorArgs = ['starts_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('starts_at', $hero->starts_at ? $hero->starts_at->format('Y-m-d\TH:i') : '')); ?>">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to start immediately</p>
                            <?php $__errorArgs = ['starts_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="expires_at" class="form-label">End Date</label>
                            <input type="datetime-local" 
                                   id="expires_at" 
                                   name="expires_at" 
                                   class="form-input <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('expires_at', $hero->expires_at ? $hero->expires_at->format('Y-m-d\TH:i') : '')); ?>">
                            <p class="text-sm text-gray-500 mt-1">Leave empty to never expire</p>
                            <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   class="form-checkbox" 
                                   <?php echo e(old('is_active', $hero->is_active) ? 'checked' : ''); ?>>
                            <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-medium">Preview</h3>
                    </div>
                    <div class="card-body">
                        <div id="hero-preview" class="rounded-lg p-8 text-center min-h-[200px] flex flex-col justify-center" 
                             style="background: <?php echo e($hero->background_color); ?>; color: <?php echo e($hero->text_color === 'dark' ? '#1F2937' : '#FFFFFF'); ?>; text-align: <?php echo e($hero->text_position); ?>;">
                            <h2 id="preview-title" class="text-2xl font-bold mb-2"><?php echo e($hero->title); ?></h2>
                            <?php if($hero->subtitle): ?>
                                <p id="preview-subtitle" class="text-lg mb-4 opacity-90"><?php echo e($hero->subtitle); ?></p>
                            <?php else: ?>
                                <p id="preview-subtitle" class="text-lg mb-4 opacity-90" style="display: none;">Hero Subtitle</p>
                            <?php endif; ?>
                            <?php if($hero->description): ?>
                                <p id="preview-description" class="mb-6 opacity-80"><?php echo e($hero->description); ?></p>
                            <?php else: ?>
                                <p id="preview-description" class="mb-6 opacity-80" style="display: none;">Hero Description</p>
                            <?php endif; ?>
                            <div id="preview-buttons" class="flex justify-center space-x-4">
                                <div id="preview-primary-btn" class="px-6 py-2 rounded-lg bg-white text-blue-600 font-medium" style="display: <?php echo e($hero->primary_button_text ? 'block' : 'none'); ?>;">
                                    <?php echo e($hero->primary_button_text ?: 'Primary Button'); ?>

                                </div>
                                <div id="preview-secondary-btn" class="px-6 py-2 rounded-lg border-2 border-white font-medium" style="display: <?php echo e($hero->secondary_button_text ? 'block' : 'none'); ?>;">
                                    <?php echo e($hero->secondary_button_text ?: 'Secondary Button'); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="<?php echo e(route('admin.heroes.index')); ?>" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Hero</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('title');
    const subtitleInput = document.getElementById('subtitle');
    const descriptionInput = document.getElementById('description');
    const backgroundColorInput = document.getElementById('background_color_text');
    const textColorInput = document.getElementById('text_color');
    const textPositionInput = document.getElementById('text_position');
    const primaryButtonTextInput = document.getElementById('primary_button_text');
    const secondaryButtonTextInput = document.getElementById('secondary_button_text');
    const primaryButtonStyleInput = document.getElementById('primary_button_style');
    const secondaryButtonStyleInput = document.getElementById('secondary_button_style');

    const preview = document.getElementById('hero-preview');
    const previewTitle = document.getElementById('preview-title');
    const previewSubtitle = document.getElementById('preview-subtitle');
    const previewDescription = document.getElementById('preview-description');
    const previewPrimaryBtn = document.getElementById('preview-primary-btn');
    const previewSecondaryBtn = document.getElementById('preview-secondary-btn');

    // Color picker sync
    document.getElementById('background_color').addEventListener('input', function() {
        document.getElementById('background_color_text').value = this.value;
        updatePreview();
    });

    // Update preview function
    function updatePreview() {
        previewTitle.textContent = titleInput.value || 'Hero Title';
        previewSubtitle.textContent = subtitleInput.value || 'Hero Subtitle';
        previewDescription.textContent = descriptionInput.value || 'Hero Description';
        
        // Update background
        preview.style.background = backgroundColorInput.value || '#3B82F6';
        
        // Update text color
        const textColor = textColorInput.value === 'dark' ? '#1F2937' : '#FFFFFF';
        preview.style.color = textColor;
        
        // Update text position
        const textAlign = textPositionInput.value || 'center';
        preview.style.textAlign = textAlign;
        
        // Update buttons
        if (primaryButtonTextInput.value) {
            previewPrimaryBtn.textContent = primaryButtonTextInput.value;
            previewPrimaryBtn.style.display = 'block';

            // Apply button style
            const primaryStyle = primaryButtonStyleInput.value;
            previewPrimaryBtn.className = 'px-6 py-2 rounded-lg font-medium';
            if (primaryStyle === 'primary') {
                previewPrimaryBtn.className += ' bg-gradient-to-r from-blue-500 to-purple-600 text-white';
            } else if (primaryStyle === 'secondary') {
                previewPrimaryBtn.className += ' bg-white text-blue-600';
            } else if (primaryStyle === 'outline') {
                previewPrimaryBtn.className += ' border-2 border-white text-white bg-transparent';
            }
        } else {
            previewPrimaryBtn.style.display = 'none';
        }

        if (secondaryButtonTextInput.value) {
            previewSecondaryBtn.textContent = secondaryButtonTextInput.value;
            previewSecondaryBtn.style.display = 'block';

            // Apply button style
            const secondaryStyle = secondaryButtonStyleInput.value;
            previewSecondaryBtn.className = 'px-6 py-2 rounded-lg font-medium';
            if (secondaryStyle === 'primary') {
                previewSecondaryBtn.className += ' bg-gradient-to-r from-blue-500 to-purple-600 text-white';
            } else if (secondaryStyle === 'secondary') {
                previewSecondaryBtn.className += ' bg-white text-blue-600';
            } else if (secondaryStyle === 'outline') {
                previewSecondaryBtn.className += ' border-2 border-white text-white bg-transparent';
            }
        } else {
            previewSecondaryBtn.style.display = 'none';
        }
        
        // Hide subtitle and description if empty
        previewSubtitle.style.display = subtitleInput.value ? 'block' : 'none';
        previewDescription.style.display = descriptionInput.value ? 'block' : 'none';
    }

    // Add event listeners
    [titleInput, subtitleInput, descriptionInput, backgroundColorInput, textColorInput, textPositionInput, primaryButtonTextInput, secondaryButtonTextInput, primaryButtonStyleInput, secondaryButtonStyleInput].forEach(input => {
        if (input) {
            input.addEventListener('input', updatePreview);
            input.addEventListener('change', updatePreview); // For select elements
        }
    });

    // Sync text input with color picker
    backgroundColorInput.addEventListener('input', function() {
        if (this.value.startsWith('#') && this.value.length === 7) {
            document.getElementById('background_color').value = this.value;
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Talha\blog-app\resources\views/admin/heroes/edit.blade.php ENDPATH**/ ?>