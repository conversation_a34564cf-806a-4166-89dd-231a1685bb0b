@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                🏷️ Browse by Category
            </h1>
            <p class="text-xl text-emerald-100 max-w-2xl mx-auto">
                Discover deals, coupons, and content organized by your favorite categories. 
                Find exactly what you're looking for!
            </p>
        </div>
    </div>
</section>

<!-- Search -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-md mx-auto">
            <form action="{{ route('categories.index') }}" method="GET" class="relative">
                <input type="text" 
                       name="search" 
                       placeholder="Search categories..." 
                       value="{{ request('search') }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Categories Grid -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(request('search'))
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">
                    Search Results for "{{ request('search') }}"
                </h2>
                <p class="text-gray-600 mt-1">{{ $categories->count() ?? 0 }} {{ Str::plural('category', $categories->count() ?? 0) }} found</p>
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @forelse($categories ?? [] as $category)
                <a href="{{ route('categories.show', $category->slug) }}" 
                   class="card-hover group">
                    <div class="p-6 text-center">
                        <!-- Category Image/Icon -->
                        @if($category->image)
                            <img src="{{ asset('storage/' . $category->image) }}" 
                                 alt="{{ $category->name }}" 
                                 class="w-16 h-16 mx-auto rounded-lg object-cover mb-4 group-hover:scale-110 transition-transform duration-300">
                        @elseif($category->icon)
                            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-3xl">
                                {!! $category->icon !!}
                            </div>
                        @else
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center group-hover:from-emerald-500 group-hover:to-teal-600 transition-colors duration-300">
                                <span class="text-white text-2xl font-bold">{{ substr($category->name, 0, 1) }}</span>
                            </div>
                        @endif

                        <!-- Category Name -->
                        <h3 class="font-bold text-lg text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors duration-200">
                            {{ $category->name }}
                        </h3>

                        <!-- Category Description -->
                        @if($category->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $category->description }}</p>
                        @endif

                        <!-- Category Stats -->
                        <div class="flex justify-center space-x-4 text-sm text-gray-500">
                            @if($category->blogs_count > 0)
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    {{ $category->blogs_count }} {{ Str::plural('article', $category->blogs_count) }}
                                </span>
                            @endif
                            
                            @if($category->coupons_count > 0)
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                    {{ $category->coupons_count }} {{ Str::plural('deal', $category->coupons_count) }}
                                </span>
                            @endif
                        </div>
                    </div>
                </a>
            @empty
                <!-- Demo Categories -->
                @php
                    $demoCategories = [
                        ['name' => 'Fashion & Clothing', 'icon' => '👗', 'description' => 'Latest fashion trends, clothing deals, and style tips', 'blogs' => 25, 'deals' => 45],
                        ['name' => 'Electronics', 'icon' => '📱', 'description' => 'Gadgets, smartphones, laptops, and tech reviews', 'blogs' => 18, 'deals' => 32],
                        ['name' => 'Food & Dining', 'icon' => '🍕', 'description' => 'Restaurant deals, recipes, and food delivery offers', 'blogs' => 22, 'deals' => 28],
                        ['name' => 'Travel', 'icon' => '✈️', 'description' => 'Travel guides, flight deals, and hotel discounts', 'blogs' => 15, 'deals' => 18],
                        ['name' => 'Home & Garden', 'icon' => '🏠', 'description' => 'Home improvement, furniture, and garden supplies', 'blogs' => 20, 'deals' => 25],
                        ['name' => 'Health & Beauty', 'icon' => '💄', 'description' => 'Skincare, makeup, wellness, and health products', 'blogs' => 16, 'deals' => 22],
                        ['name' => 'Sports & Fitness', 'icon' => '⚽', 'description' => 'Athletic wear, fitness equipment, and sports gear', 'blogs' => 12, 'deals' => 15],
                        ['name' => 'Books & Education', 'icon' => '📚', 'description' => 'Books, courses, and educational resources', 'blogs' => 14, 'deals' => 12],
                        ['name' => 'Automotive', 'icon' => '🚗', 'description' => 'Car accessories, maintenance, and automotive deals', 'blogs' => 8, 'deals' => 8],
                        ['name' => 'Pet Supplies', 'icon' => '🐕', 'description' => 'Pet food, toys, and accessories for your furry friends', 'blogs' => 10, 'deals' => 10],
                        ['name' => 'Baby & Kids', 'icon' => '👶', 'description' => 'Baby products, toys, and children\'s clothing', 'blogs' => 13, 'deals' => 14],
                        ['name' => 'Office & Business', 'icon' => '💼', 'description' => 'Office supplies, business tools, and productivity apps', 'blogs' => 9, 'deals' => 9],
                    ];
                @endphp

                @foreach($demoCategories as $category)
                    <div class="card-hover group">
                        <div class="p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-3xl">
                                {{ $category['icon'] }}
                            </div>
                            <h3 class="font-bold text-lg text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors duration-200">
                                {{ $category['name'] }}
                            </h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $category['description'] }}</p>
                            <div class="flex justify-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    {{ $category['blogs'] }} articles
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                    {{ $category['deals'] }} deals
                                </span>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endforelse
        </div>

        <!-- Pagination -->
        @if(isset($categories) && $categories->hasPages())
            <div class="mt-12">
                {{ $categories->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</section>

<!-- Popular Categories -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Most Popular Categories</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                These categories have the most active deals and content right now.
            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @php
                $popularCategories = [
                    ['name' => 'Fashion', 'count' => 45, 'color' => 'pink'],
                    ['name' => 'Electronics', 'count' => 32, 'color' => 'blue'],
                    ['name' => 'Food', 'count' => 28, 'color' => 'orange'],
                    ['name' => 'Travel', 'count' => 18, 'color' => 'green'],
                    ['name' => 'Home', 'count' => 25, 'color' => 'purple'],
                    ['name' => 'Beauty', 'count' => 22, 'color' => 'red'],
                ];
            @endphp
            
            @foreach($popularCategories as $category)
                <div class="text-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="w-12 h-12 mx-auto mb-2 bg-{{ $category['color'] }}-100 rounded-full flex items-center justify-center">
                        <div class="w-6 h-6 bg-{{ $category['color'] }}-500 rounded-full"></div>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">{{ $category['name'] }}</h3>
                    <p class="text-sm text-gray-500">{{ $category['count'] }} deals</p>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Newsletter -->
<section class="py-16 bg-gradient-to-r from-emerald-600 to-teal-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Stay Updated with New Categories</h2>
        <p class="text-xl text-emerald-100 mb-8">
            Be the first to know when we add new categories and exclusive deals.
        </p>
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input type="email" 
                   placeholder="Enter your email address" 
                   class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-300">
            <button type="submit" 
                    class="btn-primary bg-white text-emerald-600 hover:bg-gray-100 font-semibold px-8 py-3">
                Subscribe
            </button>
        </form>
    </div>
</section>
@endsection
