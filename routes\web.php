<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\BlogController as AdminBlogController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Http\Controllers\Admin\StoreController as AdminStoreController;
use App\Http\Controllers\Admin\CouponController as AdminCouponController;
use App\Http\Controllers\Admin\BannerController as AdminBannerController;
use App\Http\Controllers\Admin\TagController as AdminTagController;
use App\Http\Controllers\Admin\SettingsController as AdminSettingsController;
use App\Http\Controllers\Admin\HeroController as AdminHeroController;
use App\Http\Controllers\Admin\PartnerController as AdminPartnerController;
use App\Http\Controllers\ProfileController;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');

// Hidden promotions page (accessible only via direct link)
Route::get('/promotions', [HomeController::class, 'promotions'])->name('promotions');

// Offline page
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// Blog routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/category/{slug}', [BlogController::class, 'category'])->name('blog.category');
Route::get('/blog/tag/{slug}', [BlogController::class, 'tag'])->name('blog.tag');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Category routes
Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{slug}', [CategoryController::class, 'show'])->name('categories.show');
Route::get('/categories/{slug}/ajax', [CategoryController::class, 'ajax'])->name('categories.ajax');

// Store routes
Route::get('/stores', [StoreController::class, 'index'])->name('stores.index');
Route::get('/stores/{slug}', [StoreController::class, 'show'])->name('stores.show');
Route::get('/stores/{slug}/visit', [StoreController::class, 'visit'])->name('stores.visit');

// Coupon routes
Route::get('/coupons', [CouponController::class, 'index'])->name('coupons.index');
Route::get('/coupons/category/{slug}', [CouponController::class, 'category'])->name('coupons.category');
Route::get('/coupons/{slug}', [CouponController::class, 'show'])->name('coupons.show');
Route::post('/coupons/{coupon}/click', [CouponController::class, 'click'])->name('coupons.click');

// Search
Route::get('/search', [HomeController::class, 'search'])->name('search');

// Dashboard for authenticated users
Route::get('/dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {

    // Dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');
    Route::get('/system-info', [AdminController::class, 'systemInfo'])->name('system-info');
    Route::post('/clear-cache', [AdminController::class, 'clearCache'])->name('clear-cache');

    // Blog Management
    Route::resource('blogs', AdminBlogController::class);
    Route::post('blogs/{blog}/toggle-published', [AdminBlogController::class, 'togglePublished'])->name('blogs.toggle-published');
    Route::post('blogs/{blog}/toggle-featured', [AdminBlogController::class, 'toggleFeatured'])->name('blogs.toggle-featured');

    // Category Management
    Route::resource('categories', AdminCategoryController::class);
    Route::post('categories/{category}/toggle-active', [AdminCategoryController::class, 'toggleActive'])->name('categories.toggle-active');

    // Banner Management
    Route::resource('banners', AdminBannerController::class);
    Route::post('banners/{banner}/toggle-active', [AdminBannerController::class, 'toggleActive'])->name('banners.toggle-active');
    Route::get('banners-trash', [AdminBannerController::class, 'trash'])->name('banners.trash');
    Route::post('banners/{id}/restore', [AdminBannerController::class, 'restore'])->name('banners.restore');
    Route::delete('banners/{id}/force-delete', [AdminBannerController::class, 'forceDelete'])->name('banners.force-delete');

    // Hero Management
    Route::resource('heroes', AdminHeroController::class);
    Route::post('heroes/{hero}/toggle-active', [AdminHeroController::class, 'toggleActive'])->name('heroes.toggle-active');
    Route::get('heroes-trash', [AdminHeroController::class, 'trash'])->name('heroes.trash');
    Route::post('heroes/{id}/restore', [AdminHeroController::class, 'restore'])->name('heroes.restore');
    Route::delete('heroes/{id}/force-delete', [AdminHeroController::class, 'forceDelete'])->name('heroes.force-delete');

    // Partner Management
    Route::resource('partners', AdminPartnerController::class);
    Route::post('partners/{partner}/toggle-active', [AdminPartnerController::class, 'toggleActive'])->name('partners.toggle-active');
    Route::post('partners/{partner}/toggle-featured', [AdminPartnerController::class, 'toggleFeatured'])->name('partners.toggle-featured');

    // Tag Management
    Route::resource('tags', AdminTagController::class);
    Route::post('tags/{tag}/toggle-active', [AdminTagController::class, 'toggleActive'])->name('tags.toggle-active');

    // Settings Management
    Route::get('settings/banners', [AdminSettingsController::class, 'banners'])->name('settings.banners');
    Route::put('settings/banners', [AdminSettingsController::class, 'updateBanners'])->name('settings.banners.update');
    Route::get('settings/heroes', [AdminSettingsController::class, 'heroes'])->name('settings.heroes');
    Route::put('settings/heroes', [AdminSettingsController::class, 'updateHeroes'])->name('settings.heroes.update');
    Route::get('settings/general', [AdminSettingsController::class, 'general'])->name('settings.general');
    Route::put('settings/general', [AdminSettingsController::class, 'updateGeneral'])->name('settings.general.update');

    // Store Management
    Route::resource('stores', AdminStoreController::class);
    Route::post('stores/{store}/toggle-active', [AdminStoreController::class, 'toggleActive'])->name('stores.toggle-active');
    Route::post('stores/{store}/toggle-featured', [AdminStoreController::class, 'toggleFeatured'])->name('stores.toggle-featured');

    // Coupon Management
    Route::resource('coupons', AdminCouponController::class);
    Route::post('coupons/{coupon}/toggle-active', [AdminCouponController::class, 'toggleActive'])->name('coupons.toggle-active');
    Route::post('coupons/{coupon}/toggle-featured', [AdminCouponController::class, 'toggleFeatured'])->name('coupons.toggle-featured');
    Route::post('coupons/{coupon}/toggle-verified', [AdminCouponController::class, 'toggleVerified'])->name('coupons.toggle-verified');
    Route::post('coupons/bulk-update-expiration', [AdminCouponController::class, 'bulkUpdateExpiration'])->name('coupons.bulk-update-expiration');
});

require __DIR__.'/auth.php';
