import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                "resources/css/app.css",
                "resources/css/lazy-loading.css",
                "resources/css/swiper-custom.css",
                "resources/js/app.js",
                "resources/js/lazy-loading.js",
                "resources/js/asset-optimizer.js",
                "resources/js/swiper-config.js",
            ],
            refresh: true,
        }),
    ],
    build: {
        // Enable minification
        minify: "terser",
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            },
        },
        // Enable CSS code splitting
        cssCodeSplit: true,
        // Rollup options for better optimization
        rollupOptions: {
            output: {
                // Manual chunks for better caching
                manualChunks: {
                    vendor: ["alpinejs"],
                    utils: ["resources/js/lazy-loading.js"],
                },
                // Asset file naming
                assetFileNames: `assets/[name]-[hash][extname]`,
                // Chunk file naming
                chunkFileNames: "js/[name]-[hash].js",
                entryFileNames: "js/[name]-[hash].js",
            },
        },
        // Chunk size warning limit
        chunkSizeWarningLimit: 1000,
    },
    // CSS preprocessing options
    css: {
        devSourcemap: true,
    },
    // Server options for development
    server: {
        host: "127.0.0.1",
        port: 5173,
        cors: true,
        hmr: {
            host: "127.0.0.1",
            port: 5173,
        },
    },
});
