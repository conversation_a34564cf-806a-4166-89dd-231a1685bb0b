<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\Category;
use App\Models\Store;
use App\Models\Coupon;
use App\Models\AffiliateClick;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the admin dashboard.
     */
    public function dashboard(): View
    {
        $stats = Cache::remember('admin_dashboard_stats', 300, function () {
            // Content statistics
            $totalBlogs = Blog::count();
            $publishedBlogs = Blog::published()->count();
            $totalCategories = Category::count();
            $activeCategories = Category::active()->count();
            $totalStores = Store::count();
            $activeStores = Store::active()->count();
            $totalCoupons = Coupon::count();
            $activeCoupons = Coupon::active()->count();
            $expiredCoupons = Coupon::expired()->count();

            // Recent activity
            $recentBlogs = Blog::with('author')->latest()->limit(5)->get();
            $recentCoupons = Coupon::with('store')->latest()->limit(5)->get();
            $recentStores = Store::latest()->limit(5)->get();

            // Click statistics
            $totalClicks = AffiliateClick::count();
            $clicksToday = AffiliateClick::today()->count();
            $clicksThisWeek = AffiliateClick::thisWeek()->count();
            $clicksThisMonth = AffiliateClick::thisMonth()->count();

            // Top performing content
            $topStores = Store::withCount('affiliateClicks')
                            ->orderBy('affiliate_clicks_count', 'desc')
                            ->limit(5)
                            ->get();

            $topCoupons = Coupon::orderBy('usage_count', 'desc')
                               ->with('store')
                               ->limit(5)
                               ->get();

            $topBlogs = Blog::orderBy('views_count', 'desc')
                          ->limit(5)
                          ->get();

            return compact(
                'totalBlogs', 'publishedBlogs', 'totalCategories', 'activeCategories',
                'totalStores', 'activeStores', 'totalCoupons', 'activeCoupons', 'expiredCoupons',
                'recentBlogs', 'recentCoupons', 'recentStores',
                'totalClicks', 'clicksToday', 'clicksThisWeek', 'clicksThisMonth',
                'topStores', 'topCoupons', 'topBlogs'
            );
        });

        // Chart data for the last 30 days
        $chartData = Cache::remember('admin_chart_data', 1800, function () {
            $dates = collect();
            $clicks = collect();
            $blogs = collect();
            $coupons = collect();

            for ($i = 29; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dates->push($date->format('M j'));
                
                $clicks->push(AffiliateClick::whereDate('clicked_at', $date)->count());
                $blogs->push(Blog::whereDate('created_at', $date)->count());
                $coupons->push(Coupon::whereDate('created_at', $date)->count());
            }

            return [
                'dates' => $dates->toArray(),
                'clicks' => $clicks->toArray(),
                'blogs' => $blogs->toArray(),
                'coupons' => $coupons->toArray(),
            ];
        });

        return view('admin.dashboard', array_merge($stats, compact('chartData')));
    }

    /**
     * Clear all caches.
     */
    public function clearCache(Request $request)
    {
        // Clear Laravel cache
        Cache::flush();

        // Clear response cache if available
        if (class_exists('\Spatie\ResponseCache\Facades\ResponseCache')) {
            \Spatie\ResponseCache\Facades\ResponseCache::clear();
        }

        // Clear config and route cache
        \Artisan::call('config:clear');
        \Artisan::call('route:clear');
        \Artisan::call('view:clear');

        return back()->with('success', 'All caches have been cleared successfully!');
    }

    /**
     * Show system information.
     */
    public function systemInfo(): View
    {
        $info = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connection' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'mail_driver' => config('mail.default'),
            'timezone' => config('app.timezone'),
            'debug_mode' => config('app.debug'),
            'environment' => config('app.env'),
        ];

        // Disk usage
        $diskUsage = [
            'total' => disk_total_space(base_path()),
            'free' => disk_free_space(base_path()),
        ];
        $diskUsage['used'] = $diskUsage['total'] - $diskUsage['free'];
        $diskUsage['usage_percentage'] = ($diskUsage['used'] / $diskUsage['total']) * 100;

        // Memory usage
        $memoryUsage = [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit'),
        ];

        return view('admin.system-info', compact('info', 'diskUsage', 'memoryUsage'));
    }

    /**
     * Show analytics data.
     */
    public function analytics(Request $request): View
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        $data = Cache::remember("admin_analytics_{$period}", 1800, function () use ($startDate) {
            // Click analytics
            $clicksByDate = AffiliateClick::where('clicked_at', '>=', $startDate)
                                        ->selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
                                        ->groupBy('date')
                                        ->orderBy('date')
                                        ->pluck('clicks', 'date');

            $clicksByDevice = AffiliateClick::where('clicked_at', '>=', $startDate)
                                          ->selectRaw('device_type, COUNT(*) as clicks')
                                          ->groupBy('device_type')
                                          ->pluck('clicks', 'device_type');

            $clicksByCountry = AffiliateClick::where('clicked_at', '>=', $startDate)
                                           ->whereNotNull('country')
                                           ->selectRaw('country, COUNT(*) as clicks')
                                           ->groupBy('country')
                                           ->orderBy('clicks', 'desc')
                                           ->limit(10)
                                           ->pluck('clicks', 'country');

            // Top performing content
            $topStoreClicks = Store::withCount(['affiliateClicks' => function ($query) use ($startDate) {
                                    $query->where('clicked_at', '>=', $startDate);
                                }])
                                ->orderBy('affiliate_clicks_count', 'desc')
                                ->limit(10)
                                ->get();

            $topCouponClicks = Coupon::withCount(['affiliateClicks' => function ($query) use ($startDate) {
                                     $query->where('clicked_at', '>=', $startDate);
                                 }])
                                 ->with('store')
                                 ->orderBy('affiliate_clicks_count', 'desc')
                                 ->limit(10)
                                 ->get();

            return compact(
                'clicksByDate', 'clicksByDevice', 'clicksByCountry',
                'topStoreClicks', 'topCouponClicks'
            );
        });

        return view('admin.analytics', array_merge($data, compact('period')));
    }
}
