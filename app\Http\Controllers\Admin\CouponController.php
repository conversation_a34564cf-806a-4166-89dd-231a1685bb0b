<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Store;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Carbon\Carbon;

class CouponController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of coupons.
     */
    public function index(Request $request): View
    {
        $search = $request->get('search');
        $store = $request->get('store');
        $category = $request->get('category');
        $status = $request->get('status');
        $type = $request->get('type');

        $query = Coupon::with(['store', 'category']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        if ($store) {
            $query->where('store_id', $store);
        }

        if ($category) {
            $query->where('category_id', $category);
        }

        if ($status === 'active') {
            $query->active();
        } elseif ($status === 'expired') {
            $query->expired();
        } elseif ($status === 'inactive') {
            $query->where('is_active', false);
        } elseif ($status === 'featured') {
            $query->featured();
        } elseif ($status === 'expiring_soon') {
            $query->whereNotNull('expires_at')
                  ->where('expires_at', '>', now())
                  ->where('expires_at', '<=', now()->addDays(7));
        }

        if ($type) {
            $query->byType($type);
        }

        $coupons = $query->latest()->paginate(20);
        
        $stores = Store::active()->pluck('name', 'id');
        $categories = Category::active()->pluck('name', 'id');

        return view('admin.coupons.index', compact('coupons', 'stores', 'categories'));
    }

    /**
     * Show the form for creating a new coupon.
     */
    public function create(): View
    {
        $stores = Store::active()->pluck('name', 'id');
        $categories = Category::active()->pluck('name', 'id');
        
        return view('admin.coupons.create', compact('stores', 'categories'));
    }

    /**
     * Store a newly created coupon.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'code' => 'nullable|string|max:50',
            'type' => 'required|in:coupon,deal,cashback,freebie',
            'discount_type' => 'nullable|in:percentage,fixed,free_shipping,bogo',
            'discount_value' => 'nullable|numeric|min:0',
            'currency' => 'required|string|size:3',
            'minimum_order' => 'nullable|numeric|min:0',
            'affiliate_url' => 'required|url',
            'store_id' => 'required|exists:stores,id',
            'category_id' => 'nullable|exists:categories,id',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'expires_at' => 'nullable|date|after:starts_at',
            'is_exclusive' => 'boolean',
            'is_verified' => 'boolean',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'terms_conditions' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['meta_keywords']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;
        $data['sort_order'] = $data['sort_order'] ?? 0;

        // Parse dates
        if ($data['starts_at']) {
            $data['starts_at'] = Carbon::parse($data['starts_at']);
        }
        if ($data['expires_at']) {
            $data['expires_at'] = Carbon::parse($data['expires_at']);
        }

        $coupon = Coupon::create($data);

        // Clear relevant caches
        $this->clearCouponCaches();

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon created successfully!');
    }

    /**
     * Display the specified coupon.
     */
    public function show(Coupon $coupon): View
    {
        $coupon->load(['store', 'category']);
        $coupon->loadCount('affiliateClicks');
        
        // Get click statistics
        $clickStats = [
            'total_clicks' => $coupon->affiliateClicks()->count(),
            'clicks_today' => $coupon->affiliateClicks()->today()->count(),
            'clicks_this_week' => $coupon->affiliateClicks()->thisWeek()->count(),
            'clicks_this_month' => $coupon->affiliateClicks()->thisMonth()->count(),
        ];

        return view('admin.coupons.show', compact('coupon', 'clickStats'));
    }

    /**
     * Show the form for editing the specified coupon.
     */
    public function edit(Coupon $coupon): View
    {
        $stores = Store::active()->pluck('name', 'id');
        $categories = Category::active()->pluck('name', 'id');
        
        return view('admin.coupons.edit', compact('coupon', 'stores', 'categories'));
    }

    /**
     * Update the specified coupon.
     */
    public function update(Request $request, Coupon $coupon): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'code' => 'nullable|string|max:50',
            'type' => 'required|in:coupon,deal,cashback,freebie',
            'discount_type' => 'nullable|in:percentage,fixed,free_shipping,bogo',
            'discount_value' => 'nullable|numeric|min:0',
            'currency' => 'required|string|size:3',
            'minimum_order' => 'nullable|numeric|min:0',
            'affiliate_url' => 'required|url',
            'store_id' => 'required|exists:stores,id',
            'category_id' => 'nullable|exists:categories,id',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
            'is_exclusive' => 'boolean',
            'is_verified' => 'boolean',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'terms_conditions' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->except(['meta_keywords']);
        $data['meta_keywords'] = $request->meta_keywords ? array_filter($request->meta_keywords) : null;

        // Parse dates
        if ($data['starts_at']) {
            $data['starts_at'] = Carbon::parse($data['starts_at']);
        }
        if ($data['expires_at']) {
            $data['expires_at'] = Carbon::parse($data['expires_at']);
        }

        $oldSlug = $coupon->slug;
        $coupon->update($data);

        // Clear relevant caches
        $this->clearCouponCaches($oldSlug, $coupon->slug);

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon updated successfully!');
    }

    /**
     * Remove the specified coupon.
     */
    public function destroy(Coupon $coupon): RedirectResponse
    {
        $slug = $coupon->slug;
        $coupon->delete();

        // Clear relevant caches
        $this->clearCouponCaches($slug);

        return redirect()->route('admin.coupons.index')
                        ->with('success', 'Coupon deleted successfully!');
    }

    /**
     * Toggle coupon active status.
     */
    public function toggleActive(Coupon $coupon): RedirectResponse
    {
        $coupon->update(['is_active' => !$coupon->is_active]);
        
        $this->clearCouponCaches($coupon->slug);

        $status = $coupon->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Coupon {$status} successfully!");
    }

    /**
     * Toggle coupon featured status.
     */
    public function toggleFeatured(Coupon $coupon): RedirectResponse
    {
        $coupon->update(['is_featured' => !$coupon->is_featured]);
        
        $this->clearCouponCaches($coupon->slug);

        $status = $coupon->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Coupon {$status} successfully!");
    }

    /**
     * Toggle coupon verified status.
     */
    public function toggleVerified(Coupon $coupon): RedirectResponse
    {
        $coupon->update(['is_verified' => !$coupon->is_verified]);
        
        $this->clearCouponCaches($coupon->slug);

        $status = $coupon->is_verified ? 'verified' : 'unverified';
        return back()->with('success', "Coupon {$status} successfully!");
    }

    /**
     * Bulk update coupon expiration dates.
     */
    public function bulkUpdateExpiration(Request $request): RedirectResponse
    {
        $request->validate([
            'coupon_ids' => 'required|array',
            'coupon_ids.*' => 'exists:coupons,id',
            'expires_at' => 'required|date|after:today',
        ]);

        $expiresAt = Carbon::parse($request->expires_at);
        
        Coupon::whereIn('id', $request->coupon_ids)
              ->update(['expires_at' => $expiresAt]);

        $this->clearCouponCaches();

        $count = count($request->coupon_ids);
        return back()->with('success', "Updated expiration date for {$count} coupons successfully!");
    }

    /**
     * Clear coupon-related caches.
     */
    private function clearCouponCaches(string $oldSlug = null, string $newSlug = null): void
    {
        // Clear general caches
        Cache::forget('homepage_data');
        Cache::forget('promotions_data');
        
        // Clear specific coupon caches
        if ($oldSlug) {
            Cache::forget("coupon_{$oldSlug}");
            Cache::forget("related_coupons_{$oldSlug}");
        }
        
        if ($newSlug && $newSlug !== $oldSlug) {
            Cache::forget("coupon_{$newSlug}");
            Cache::forget("related_coupons_{$newSlug}");
        }

        // Clear coupons listing caches
        for ($i = 1; $i <= 10; $i++) {
            Cache::forget("coupons_newest_{$i}");
            Cache::forget("coupons_popular_{$i}");
            Cache::forget("coupons_expiring_{$i}");
        }

        // Clear response cache if available
        if (class_exists('\Spatie\ResponseCache\Facades\ResponseCache')) {
            \Spatie\ResponseCache\Facades\ResponseCache::clear();
        }
    }
}
