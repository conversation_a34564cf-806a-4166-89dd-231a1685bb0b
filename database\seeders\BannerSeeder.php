<?php

namespace Database\Seeders;

use App\Models\Banner;
use Illuminate\Database\Seeder;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banners = [
            [
                'title' => 'Special Offer Banner',
                'description' => 'Limited time offer - Get 50% off on premium content',
                'image' => 'banners/banner-1.jpg',
                'link_url' => '/blog',
                'link_text' => 'Shop Now',
                'link_target' => '_self',
                'button_style' => 'primary',
                'text_position' => 'center',
                'text_color' => 'white',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'New Collection Launch',
                'description' => 'Discover our latest blog articles and insights',
                'image' => 'banners/banner-2.jpg',
                'link_url' => '/blog?featured=1',
                'link_text' => 'Explore Now',
                'link_target' => '_self',
                'button_style' => 'outline',
                'text_position' => 'left',
                'text_color' => 'white',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Summer Reading Campaign',
                'description' => 'Join thousands of readers in our summer reading challenge',
                'image' => 'banners/banner-3.jpg',
                'link_url' => '/blog?category=lifestyle',
                'link_text' => 'Join Challenge',
                'link_target' => '_self',
                'button_style' => 'primary',
                'text_position' => 'right',
                'text_color' => 'white',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Tech Trends 2024',
                'description' => 'Stay ahead with the latest technology trends and insights',
                'image' => 'banners/banner-4.jpg',
                'link_url' => '/blog?category=technology',
                'link_text' => 'Read More',
                'link_target' => '_self',
                'button_style' => 'secondary',
                'text_position' => 'center',
                'text_color' => 'dark',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'title' => 'Health & Wellness Guide',
                'description' => 'Transform your lifestyle with our comprehensive health guides',
                'image' => 'banners/banner-5.jpg',
                'link_url' => '/blog?category=health',
                'link_text' => 'Start Journey',
                'link_target' => '_self',
                'button_style' => 'primary',
                'text_position' => 'left',
                'text_color' => 'white',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($banners as $banner) {
            Banner::create($banner);
        }
    }
}
